import axios from "../http";
import Vue from "vue";

// 获取角色列表
function getRoleList(data) {
  return axios
    .get(`/api/permission-service/role/list`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 角色信息
function getRoleInfo(data) {
  return axios
    .get(`/api/permission-service/role/info?role_id=${data.role_id}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 创建角色
function getRoleCreate(data) {
  return axios
    .post(`/api/permission-service/role/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 更新角色
function getRoleUpdate(data) {
  return axios
    .patch(`/api/permission-service/role/update?role_id=${data.role_id}`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 删除角色
function roleDelete(data) {
  return axios
    .delete(`/api/permission-service/role/delete?role_id=${data.role_id}`)
    .then((response) => {
      if (response.status === 200) {
        Vue.prototype.$message.success("删除成功");
      }
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 角色禁用启用
function roleOpenClose(data) {
  return axios
    .get(`/api/permission-service/role/open-or-close?role_id=${data.role_id}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取角色权限
function getRolePermission(data) {
  return axios
    .get(`/api/permission-service/role-permission/info?role_id=${data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 编辑角色权限
function updateRolePermission(data) {
  return axios
    .post(
      `/api/permission-service/role-permission/update?role_id=${data.id}`,
      data.data
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 角色关联员工 角色下员工列表

function getRoleStaffList(data) {
  return axios
    .get(`/api/permission-service/employee/role/list?role_id=${data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 更新角色下的员工信息
function updateRoleStaff(data) {
  return axios
    .post(
      `/api/permission-service/employee/role/update?role_id=${data.id}`,
      data.data
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export default {
  // 获取角色列表
  async GetRoleList(data) {
    return getRoleList(data);
  },
  // 角色信息
  async GetRoleInfo(data) {
    return getRoleInfo(data);
  },
  // 创建角色
  async GetRoleCreate(data) {
    return getRoleCreate(data);
  },
  // 更新角色
  async GetRoleUpdate(data) {
    return getRoleUpdate(data);
  },
  // 删除区域
  async RoleDelete(data) {
    return roleDelete(data);
  },
  // 角色禁用启用
  async RoleOpenClose(data) {
    return roleOpenClose(data);
  },
  async getRolePermission(data) {
    return getRolePermission(data);
  },
  async updateRolePermission(data) {
    return updateRolePermission(data);
  },
  async getRoleStaffList(data) {
    return getRoleStaffList(data);
  },
  async updateRoleStaff(data) {
    return updateRoleStaff(data);
  }
};
