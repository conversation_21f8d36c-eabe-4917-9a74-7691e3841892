import { fetchGet, fetchPost, fetchDel, fetchPatch } from "../fetch";

// 新增权限组
function createPermissionGroup(data) {
  return fetchPost("/api/permission-service/permission/group/create", data);
}
// 删除权限组
function delPermissionGroup(data) {
  return fetchDel(
    `/api/permission-service/permission/group/delete?group_id=${data}`
  );
}
// 权限组详情
function getPermissionGroupInfo(data) {
  return fetchGet(
    `/api/permission-service/permission/group/info?group_id=${data}`
  );
}
// 权限组列表
function getPermissionGroupList(data) {
  return fetchGet("/api/permission-service/permission/group/list", {
    params: data
  });
}
// 修改权限组
function updatePermissionGroup(data) {
  return fetchPatch(
    `/api/permission-service/permission/group/update?group_id=${data.id}`,
    data
  );
}
// 创建权限
function createPemission(data) {
  return fetchPost(
    "/api/permission-service/permission/permission/create",
    data
  );
}
// 删除权限
function delPermission(data) {
  return fetchDel(
    `/api/permission-service/permission/permission/delete?permission_id=${data}`
  );
}
// 权限详情
function getPermissionInfo(data) {
  return fetchGet(
    `/api/permission-service/permission/permission/info?permission_id=${data}`
  );
}
// 修改权限
function updatePermission(data) {
  return fetchPatch(
    `/api/permission-service/permission/permission/update?permission_id=${data.id}`,
    data
  );
}
// 获取权限类型
function getPermissionType(data) {
  return fetchGet("/api/permission-service/permission/permission/type", {
    params: data
  });
}
//  获取接口方法
function getMethodList(data) {
  return fetchGet("/api/permission-service/permission/permission/method", {
    params: data
  });
}

// 刷新默认权限
function refreshDefaultPermission() {
  return fetchGet("/api/permission-service/permission/default/refresh");
}

// 创建权限类型
function createPermissionType(data) {
  return fetchPost("/api/permission-service/permission/permission/type", data);
}

// 修改权限类型
function updatePermissionType(data) {
  return fetchPatch(
    `/api/permission-service/permission/permission/type?type_id=${data.id}`,
    data
  );
}

export default {
  async createPermissionGroup(data) {
    return createPermissionGroup(data);
  },
  async delPermissionGroup(data) {
    return delPermissionGroup(data);
  },
  async getPermissionGroupInfo(data) {
    return getPermissionGroupInfo(data);
  },
  async getPermissionGroupList(data) {
    return getPermissionGroupList(data);
  },
  async updatePermissionGroup(data) {
    return updatePermissionGroup(data);
  },
  async createPemission(data) {
    return createPemission(data);
  },
  async delPermission(data) {
    return delPermission(data);
  },
  async getPermissionInfo(data) {
    return getPermissionInfo(data);
  },
  async updatePermission(data) {
    return updatePermission(data);
  },
  async getPermissionType(data) {
    return getPermissionType(data);
  },
  async getMethodList(data) {
    return getMethodList(data);
  },
  async refreshDefaultPermission(data) {
    return refreshDefaultPermission(data);
  },
  async createPermissionType(data) {
    return createPermissionType(data);
  },
  async updatePermissionType(data) {
    return updatePermissionType(data);
  }
};
