// 点名上课
import axios from "../http";
import Vue from "vue";
import qs from "qs";

//  学员概括(无新增学员)
function cockpitStuInfo(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/student-service/student-statistics/info?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
//  学员概括(新增学员)
function cockpitNewStuInfo(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/market-service/customer-statistics/get-new-student-count?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 招生人数概括(新增学员)
function cockpitCustomerInfo(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/market-service/customer-statistics/info?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 收费金额
function receiptInfo(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/order-service/admin/receipt-statistics/info?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 收费分布
function receiptDistribute(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/order-service/admin/receipt-statistics/distribute?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 课消率
function deductList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/order-service/admin/deduct/deduct-ratio?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 满班率
function getClassSize(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/school-service/classroom-statistics/class-size?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 出勤率
function getClassAttendance(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/school-service/classroom-statistics/class-attendance?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 直营校月报
function getDirectMonthReport(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/report-center-service/admin/offline-school/department-list?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 直营校月报-区域
function getDirectMonthAreaReport(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/report-center-service/admin/offline-school/area-list?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
function getDirectMonthDetail(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/report-center-service/admin/offline-school/detail-list?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
function getDirectMonthDetailExport(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/report-center-service/admin/offline-school/detail-export?${new_data}`,
      { params: { exportData: 1 } }
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
function getDirectMonthDetailDepartment(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/report-center-service/admin/offline-school/department-month?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
export default {
  async cockpitStuInfo(data) {
    return cockpitStuInfo(data);
  },
  async cockpitNewStuInfo(data) {
    return cockpitNewStuInfo(data);
  },
  async cockpitCustomerInfo(data) {
    return cockpitCustomerInfo(data);
  },
  async receiptInfo(data) {
    return receiptInfo(data);
  },
  async receiptDistribute(data) {
    return receiptDistribute(data);
  },
  async deductList(data) {
    return deductList(data);
  },
  async getClassSize(data) {
    return getClassSize(data);
  },
  async getClassAttendance(data) {
    return getClassAttendance(data);
  },
  async getDirectMonthReport(data) {
    return getDirectMonthReport(data);
  },
  async getDirectMonthAreaReport(data) {
    return getDirectMonthAreaReport(data);
  },
  async getDirectMonthDetail(data) {
    return getDirectMonthDetail(data);
  },
  async getDirectMonthDetailExport(data) {
    return getDirectMonthDetailExport(data);
  },
  async getDirectMonthDetailDepartment(data) {
    return getDirectMonthDetailDepartment(data);
  }
};
