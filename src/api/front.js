import { fetchGet } from "../fetch";
import qs from "qs";
// 客户状态分析
function getCustomerStatus(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/market-service/report/customer-status?${new_data}`);
}
function getCustomerStatusTotal(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/market-service/report/customer-status-total?${new_data}`
  );
}
// 客户状态分析导出
function exportCustomerStatus(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/market-service/report/customer-status-export?${new_data}`,
    {
      params: { exportData: 1 }
    }
  );
}

// 客户分析---市场渠道
function getCustomerSource(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/market-service/report/customer-source?${new_data}`);
}
// 客户分析---市场渠道导出
function exportCustomerSource(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/market-service/report/customer-source-export?${new_data}`,
    {
      params: { exportData: 1 }
    }
  );
}
// 客户分析---市场渠道转化成功列表
function getCustomerSourceToStudent(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });

  return fetchGet(
    `/api/market-service/report/customer-source-to-student-list?${new_data}`
  );
}
// 客户分析---市场渠道转化成功导出
function exportCustomerSourceToStudent(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/market-service/report/customer-source-to-student-list-export?${new_data}`,
    {
      params: { exportData: 1 }
    }
  );
}
// 客户分析---意向级别
function getCustomerIntention(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/market-service/report/customer-intention?${new_data}`);
}
// 客户分析---意向级别导出
function exportCustomerIntention(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/market-service/report/customer-intention-export?${new_data}`,
    {
      params: { exportData: 1 }
    }
  );
}
// 客户分析---销售漏斗
function getCustomerTransform(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });

  return fetchGet(`/api/market-service/report/customer-transform?${new_data}`);
}

// 转化率分析列表
function getCustomerTransferRate(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });

  return fetchGet(`/api/market-service/report/transfer-rate?${new_data}`);
}

// 转化率分析列表导出
function exportCustomerTransferRateExport(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/market-service/report/transfer-rate-export?${new_data}`,
    {
      params: { exportData: 1 }
    }
  );
}

// 转化率分析列表详情
function getCustomerTransferRateStudent(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });

  return fetchGet(
    `/api/market-service/report/transfer-rate-student?${new_data}`
  );
}

// 转化率分析列表详情导出
function getCustomerTransferRateStudentExport(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });

  return fetchGet(
    `/api/market-service/report/transfer-rate-student-export?${new_data}`,
    {
      params: { exportData: 1 }
    }
  );
}

// 转化率分析列表
function daylogIntroducerList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/student-service/day-log/introducer-list?${new_data}`);
}

// 转化率分析列表
function daylogList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/student-service/day-log/list?${new_data}`);
}
// 正数转化率分析列表
function daylogRegularCourseList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/student-service/day-log/regular-course-list?${new_data}`
  );
}

// 转化率分析列表详情导出
function dayLogExport(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });

  return fetchGet(`/api/student-service/day-log/export?${new_data}`, {
    params: { exportData: 1 }
  });
}
// 转化率分析列表详情导出
function dayLogStudentRegularCourseExport(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });

  return fetchGet(
    `/api/student-service/day-log/student-regular-course-export?${new_data}`,
    {
      params: { exportData: 1 }
    }
  );
}
// 转介绍统计
function reportTransferIntroduce(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/market-service/report/transfer-introduce?${new_data}`);
}

// 转介绍学员列表
function reportTransferIntroduceStudentList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/market-service/report/transfer-introduce-student?${new_data}`
  );
}

// 邀约试听列表
function auditionRateList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/market-service/report/audition-rate?${new_data}`);
}

// 转介绍统计导出
function transferIntroduceExport(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });

  return fetchGet(
    `/api/market-service/report/transfer-introduce-export?${new_data}`,
    {
      params: { exportData: 1 }
    }
  );
}

// 转介绍学员列表
function transferIntroduceStudentExport(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });

  return fetchGet(
    `/api/market-service/report/transfer-introduce-student-export?${new_data}`,
    {
      params: { exportData: 1 }
    }
  );
}
// 前端数据面板列表
function twBoardList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/order-service/admin/tw-board/list?${new_data}`);
}
// 前端数据面板导出
function twBoardListExport(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/order-service/admin/tw-board/export?${new_data}`, {
    params: { exportData: 1 }
  });
}

// 转化率分析列表详情
function getCustomerTransferRateStudented(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });

  return fetchGet(`/api/market-service/conversion/advisor?${new_data}`);
}
export default {
  getCustomerStatus,
  exportCustomerStatus,
  getCustomerStatusTotal,
  getCustomerSource,
  exportCustomerSource,
  getCustomerSourceToStudent,
  exportCustomerSourceToStudent,
  getCustomerIntention,
  exportCustomerIntention,
  getCustomerTransform,
  getCustomerTransferRate,
  exportCustomerTransferRateExport,
  getCustomerTransferRateStudent,
  getCustomerTransferRateStudentExport,
  daylogIntroducerList,
  daylogRegularCourseList,
  daylogList,
  dayLogExport,
  dayLogStudentRegularCourseExport,
  reportTransferIntroduce,
  reportTransferIntroduceStudentList,
  auditionRateList,
  transferIntroduceExport,
  transferIntroduceStudentExport,
  twBoardList,
  twBoardListExport,
  getCustomerTransferRateStudented
};
