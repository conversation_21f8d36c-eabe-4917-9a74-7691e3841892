import axios from "../http";
import Vue from "vue";
import qs from "qs";
import { fetchGet } from "../fetch";
const api_path = `/api/school-service/aboluo`;

// 人数概况
function getAboluoTotalNum(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`${api_path}/total/num?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    });
}

// 周劣质班级/优质班级列表
function getAboluoHighLowClass(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`${api_path}/high-low-class?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    });
}

// 周劣质班级/优质班级详情
function getAboluoClassDetail(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`${api_path}/class-detail?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    });
}
// 续报率详情
function getAboluoRenewDetail(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`${api_path}/renew-detail?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    });
}

// 排行榜
function getAboluoTotalRankList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`${api_path}/ranking-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    });
}
// 水球数据
function getAboluoClassroomTotal(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`${api_path}/classroom/total?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    });
}

// 导出
function getAboluoRenewDetailExport(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`${api_path}/renew-detail/export?${new_data}`, {
    params: { exportData: 1 }
  });
}

// 最高记录数据
function getAboluoRankingBestInfo(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`${api_path}/ranking-info?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    });
}
// 班主任排行榜
function getAboluoHeaderTeacherRankingList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`${api_path}/header-teacher-ranking-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    });
}

// 班主任老师排行最高
function getAboluoHeaderTeacherRankingInfo(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`${api_path}/header-teacher-ranking-info?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    });
}
// 代课老师排行榜
function getAboluoTeacherRankingList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`${api_path}/teacher-ranking-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    });
}
// 代课主讲老师排行最高
function getAboluoTeacherRankingInfo(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`${api_path}/teacher-ranking-info?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    });
}

export default {
  async getAboluoTotalNum(data) {
    return getAboluoTotalNum(data);
  },
  async getAboluoHighLowClass(data) {
    return getAboluoHighLowClass(data);
  },
  async getAboluoClassDetail(data) {
    return getAboluoClassDetail(data);
  },
  async getAboluoRenewDetail(data) {
    return getAboluoRenewDetail(data);
  },
  async getAboluoTotalRankList(data) {
    return getAboluoTotalRankList(data);
  },
  async getAboluoClassroomTotal(data) {
    return getAboluoClassroomTotal(data);
  },
  async getAboluoRenewDetailExport(data) {
    return getAboluoRenewDetailExport(data);
  },
  async getAboluoRankingBestInfo(data) {
    return getAboluoRankingBestInfo(data);
  },
  async getAboluoHeaderTeacherRankingList(data) {
    return getAboluoHeaderTeacherRankingList(data);
  },
  async getAboluoHeaderTeacherRankingInfo(data) {
    return getAboluoHeaderTeacherRankingInfo(data);
  },
  async getAboluoTeacherRankingList(data) {
    return getAboluoTeacherRankingList(data);
  },
  async getAboluoTeacherRankingInfo(data) {
    return getAboluoTeacherRankingInfo(data);
  }
};
