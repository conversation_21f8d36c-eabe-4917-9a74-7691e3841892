import { fetchGet, fetchPost } from "../fetch";
import qs from "qs";

// 邀约排行榜
function inviteRateList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/taskpool-service/admin/advisor/invite-rate?${new_data}`
  );
}

// 签单排行榜
function signRateList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/taskpool-service/admin/advisor/sign-rate?${new_data}`);
}

// 客户相关+试听相关
function advisorStudentList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/taskpool-service/admin/advisor/student/list?${new_data}`
  );
}

// 目标正课人数
function totalPerson(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/taskpool-service/admin/course/total-person?${new_data}`
  );
}
// 目标正课流水
function totalPrice(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/taskpool-service/admin/course/total-price?${new_data}`);
}
// 目标信息查询
function searchPersonPrice(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/taskpool-service/admin/target/search-person-price?${new_data}`
  );
}
// 目标新创建
function createPersonPrice(data) {
  return fetchPost(
    `/api/taskpool-service/admin/target/create-person-price`,
    data,
    "创建成功"
  );
}

// 目标修改
function savePersonPrice(data) {
  return fetchPost(
    `/api/taskpool-service/admin/target/save-person-price`,
    data,
    "修改成功"
  );
}

// 目标删除
function removePrice(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/taskpool-service/admin/target/remove?${new_data}`);
}

// 目标信息列表
function getTargetList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/taskpool-service/admin/target/list?${new_data}`);
}

export default {
  inviteRateList,
  signRateList,
  advisorStudentList,
  totalPerson,
  totalPrice,
  searchPersonPrice,
  createPersonPrice,
  savePersonPrice,
  removePrice,
  getTargetList
};
