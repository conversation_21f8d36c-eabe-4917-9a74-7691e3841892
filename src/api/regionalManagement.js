import axios from "../http";
import Vue from "vue";

// 获取区域列表
function getAreaList(data) {
  return axios
    .get(`/api/organization-service/area/list`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 区域信息
function getAreaInfo(data) {
  return axios
    .get(`/api/organization-service/area/info?area_id=${data.area_id}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 创建区域
function getAreaCreate(data) {
  return axios
    .post(`/api/organization-service/area/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 更新区域
function areaUpdate(data) {
  return axios
    .patch(
      `/api/organization-service/area/update?area_id=${data.area_id}`,
      data
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 删除区域
function areaDelete(data) {
  return axios
    .delete(`/api/organization-service/area/delete?area_id=${data.area_id}`)
    .then((response) => {
      if (response.status === 200) {
        Vue.prototype.$message.success("删除成功");
      }
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export default {
  // 获取区域列表
  async GetAreaList(data) {
    return getAreaList(data);
  },
  // 区域信息
  async GetAreaInfo(data) {
    return getAreaInfo(data);
  },
  // 创建区域
  async GetAreaCreate(data) {
    return getAreaCreate(data);
  },
  // 更新区域
  async AreaUpdate(data) {
    return areaUpdate(data);
  },
  // 删除区域
  async AreaDelete(data) {
    return areaDelete(data);
  }
};
