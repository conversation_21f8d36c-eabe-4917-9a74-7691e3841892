import axios from "../http";
import qs from "qs";
import Vue from "vue";

const onlinePaymentApi = {
  // 获取在线支付数据列表
  getList(data) {
    const new_data = qs.stringify(data, { arrayFormat: "repeat" });
    return axios
      .get(`/api/order-service/admin/trade-bill/list?${new_data}`)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        Vue.prototype.$message.error(error.err);
      })
      .finally();
  },

  // 导出数据
  export(data) {
    const new_data = qs.stringify(data, { arrayFormat: "repeat" });
    return axios
      .get(`/api/order-service/admin/trade-bill/list-export?${new_data}`)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        Vue.prototype.$message.error(error.err);
      })
      .finally();
  },

  // 大众点评导入
  importDianping(data) {
    return axios
      .post(`/api/order-service/admin/trade-bill/import-dianping`, data)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        Vue.prototype.$message.error(error.err);
      })
      .finally();
  },

  // 银联导入
  importUnion(data) {
    return axios
      .post(`/api/order-service/admin/trade-bill/import-union`, data)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        Vue.prototype.$message.error(error.err);
      })
      .finally();
  },

  // 银行转账导入
  importBank(data) {
    return axios
      .post(`/api/order-service/admin/trade-bill/import-transfer`, data)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        Vue.prototype.$message.error(error.err);
      })
      .finally();
  },

  // 获取收据明细
  getReceiptDetail(data) {
    const new_data = qs.stringify(data, { arrayFormat: "repeat" });
    return axios
      .get(`/api/order-service/admin/trade-bill/detail-receipt?${new_data}`)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        Vue.prototype.$message.error(error.err);
      })
      .finally();
  },

  // 获取易宝详情
  getYopDetail(data) {
    const new_data = qs.stringify(data, { arrayFormat: "repeat" });
    return axios
      .get(`/api/order-service/admin/trade-bill/detail-yop?${new_data}`)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        Vue.prototype.$message.error(error.err);
      })
      .finally();
  },

  // 导出易宝详情
  exportYopDetail(data) {
    const new_data = qs.stringify(data, { arrayFormat: "repeat" });
    return axios
      .get(`/api/order-service/admin/trade-bill/detail-yop-export?${new_data}`)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        Vue.prototype.$message.error(error.err);
      })
      .finally();
  },

  // 获取财付通详情
  getCftDetail(data) {
    const new_data = qs.stringify(data, { arrayFormat: "repeat" });
    return axios
      .get(`/api/order-service/admin/trade-bill/detail-cft?${new_data}`)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        Vue.prototype.$message.error(error.err);
      })
      .finally();
  },

  // 导出财付通详情
  exportCftDetail(data) {
    const new_data = qs.stringify(data, { arrayFormat: "repeat" });
    return axios
      .get(`/api/order-service/admin/trade-bill/detail-cft-export?${new_data}`)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        Vue.prototype.$message.error(error.err);
      })
      .finally();
  },

  // 导出收据明细
  exportReceiptDetail(data) {
    const new_data = qs.stringify(data, { arrayFormat: "repeat" });
    return axios
      .get(
        `/api/order-service/admin/trade-bill/detail-receipt-export?${new_data}`
      )
      .then((response) => {
        return response;
      })
      .catch((error) => {
        Vue.prototype.$message.error(error.err);
      })
      .finally();
  }
};

export default onlinePaymentApi;
