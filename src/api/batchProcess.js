import { fetchPost } from "../fetch";

/**
 * 处理单条数据
 * @param {Object} data - 待处理的数据
 * @returns {Promise}
 * @throws {Error} 当接口返回错误时抛出错误
 */
export function processItem(data, api_url) {
  return fetchPost(api_url, data, "").then((response) => {
    // 判断接口返回的code
    if (response.data.code === 0) {
      return response.data;
    } else {
      // 如果是错误，抛出错误信息
      throw new Error(response.data.message || "处理失败");
    }
  });
}
