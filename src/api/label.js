import axios from "../http";
import Vue from "vue";
// 创建标签
function createLabel(data) {
  return axios
    .post(`/api/organization-service/label/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 删除标签
function delLabel(data) {
  return axios
    .delete(`/api/organization-service/label/delete?label_id=${data.label_id}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 标签启用
function enableLabel(data) {
  return axios
    .patch(
      `/api/organization-service/label/enable?label_id=${data.label_id}`,
      data
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 标签信息
function getLabelInfo(data) {
  return axios
    .get(`/api/organization-service/label/info`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 标签列表
function getLabelList(data) {
  return axios
    .get(`/api/organization-service/label/list`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 修改标签
function updateLabel(data) {
  return axios
    .patch(
      `/api/organization-service/label/update?label_id=${data.label_id}`,
      data
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取标签树
function getLabelTree(data) {
  return axios
    .get(`/api/organization-service/label/overview`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function exportLabel(data) {
  return axios
    .get(`/api/organization-service/label/export`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export default {
  async CreateLabel(data) {
    return createLabel(data);
  },
  async DelLabel(data) {
    return delLabel(data);
  },
  async EnableLabel(data) {
    return enableLabel(data);
  },
  async GetLabelInfo(data) {
    return getLabelInfo(data);
  },
  async GetLabelList(data) {
    return getLabelList(data);
  },
  async UpdateLabel(data) {
    return updateLabel(data);
  },
  async GetLabelTree(data) {
    return getLabelTree(data);
  },
  async ExportLabel(data) {
    return exportLabel(data);
  }
};
