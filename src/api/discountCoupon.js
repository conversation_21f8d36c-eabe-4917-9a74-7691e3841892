import axios from "../http";
import qs from "qs";

import { Message } from "element-ui";
const fetch = require("../fetch");

const couponPoolUri = "/api/coupon-service/coupon-pool";
const couponUseUri = "/api/coupon-service/coupon-use";

// 优惠券池子添加
function couponPoolCreate(data) {
  return fetch.fetchPost(`${couponPoolUri}/create`, data, "");
}
// 优惠券池子修改
function couponPoolUpdate(data) {
  return fetch.fetchPost(`${couponPoolUri}/save`, data, "");
}

// 优惠券池子列表
function couponPoolList(data) {
  return fetch.fetchGet(`${couponPoolUri}/list`, {
    params: data
  });
}
// 优惠券池子详情
function couponPoolInfo(data) {
  return fetch.fetchGet(`${couponPoolUri}/info`, {
    params: data
  });
}
// 可用优惠券列表;
function getCouponCanUseList(data) {
  return fetch.fetchPost(`${couponUseUri}/can-use-list`, data, "");
}

// 可叠加优惠券列表;
function getCouponCanOverlayList(data) {
  return fetch.fetchPost(`${couponUseUri}/can-overlay-list`, data, "");
}
// 优惠券分类添加
function createcouponType(data) {
  return axios
    .post(`/api/coupon-service/coupon-category/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Message.error(error.err);
    })
    .finally();
}

function getCouponTypeinfo(data) {
  return axios
    .get(`/api/coupon-service/coupon-category/info`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Message.error(error.err);
    })
    .finally();
}

function getCouponTypeList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/coupon-service/coupon-category/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Message.error(error.err);
    })
    .finally();
}

function saveCouponType(data) {
  return axios
    .post(`/api/coupon-service/coupon-category/save`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Message.error(error.err);
    })
    .finally();
}

function delCouponType(data) {
  return axios
    .delete(`/api/coupon-service/coupon-category/delete?id=${data.id}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Message.error(error.err);
    })
    .finally();
}

function createCoupon(data) {
  return axios
    .post(`/api/coupon-service/coupon-template/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Message.error(error.err);
    })
    .finally();
}

function delCoupon(data) {
  return axios
    .delete(`/api/coupon-service/coupon-template/delete?id=${data.id}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Message.error(error.err);
    })
    .finally();
}
function delCoupons(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .delete(`/api/coupon-service/coupon-template/delete-ids?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Message.error(error.err);
    })
    .finally();
}

function getCouponInfo(data) {
  return axios
    .get(`/api/coupon-service/coupon-template/info`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Message.error(error.err);
    })
    .finally();
}

function getCouponDepartmentList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/coupon-service/coupon-template/department-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Message.error(error.err);
    })
    .finally();
}

function getCouponList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/coupon-service/coupon-template/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Message.error(error.err);
    })
    .finally();
}

function saveCouponInfo(data) {
  return axios
    .post(`/api/coupon-service/coupon-template/save`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}

function saveCouponStatus(data) {
  return axios
    .post(`/api/coupon-service/coupon-template/save-assign-status`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Message.error(error.err);
    })
    .finally();
}

function saveCouponOverlay(data) {
  return axios
    .post(`/api/coupon-service/coupon-template/save-overlay`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Message.error(error.err);
    })
    .finally();
}

function saveCouponUsedStatus(data) {
  return axios
    .post(`/api/coupon-service/coupon-template/save-used-status`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Message.error(error.err);
    })
    .finally();
}

function getCounponDetailStatus(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/coupon-service/coupon-detail/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Message.error(error.err);
    })
    .finally();
}
// 优惠券明细导出
function exportCouponDetail(data) {
  return axios
    .get(`/api/coupon-service/coupon-detail/export`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Message.error(error.err);
    })
    .finally();
}
// 给意向客户发放优惠券
function assginCustomerCoupon(data) {
  return axios
    .post(`/api/coupon-service/coupon-assign/customer`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Message.error(error.err);
    })
    .finally();
}
// 给学员发放优惠券加锁
function couponLock(data) {
  return axios
    .get(`/api/coupon-service/coupon-assign/lock-coupon`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Message.error(error.err);
    })
    .finally();
}
// 给学员发放优惠券
function assginStudentCoupon(data) {
  return axios
    .post(`/api/coupon-service/coupon-assign/student`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Message.error(error.err);
    })
    .finally();
}
// 给学员发放优惠券解锁
function couponUnlock(data) {
  return axios
    .get(`/api/coupon-service/coupon-assign/unlock-coupon`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Message.error(error.err);
    })
    .finally();
}
// 优惠券明细用户是否可用状态修改
function saveAvailable(data) {
  return axios
    .post(`/api/coupon-service/coupon-detail/save-available`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Message.error(error.err);
    })
    .finally();
}
// 校区下的优惠卷可发送总数量
function getCouponNumber(data) {
  return axios
    .get(`/api/coupon-service/coupon-assign/school-coupon-num`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Message.error(error.err);
    })
    .finally();
}

export default {
  couponPoolCreate,
  couponPoolList,
  couponPoolInfo,
  couponPoolUpdate,
  getCouponCanUseList,
  getCouponCanOverlayList,
  async CreatecouponType(data) {
    return createcouponType(data);
  },
  async GetCouponTypeinfo(data) {
    return getCouponTypeinfo(data);
  },
  async GetCouponTypeList(data) {
    return getCouponTypeList(data);
  },
  async SaveCouponType(data) {
    return saveCouponType(data);
  },
  async DelCouponType(data) {
    return delCouponType(data);
  },
  async DelCoupon(data) {
    return delCoupon(data);
  },
  async DelCoupons(data) {
    return delCoupons(data);
  },
  async CreateCoupon(data) {
    return createCoupon(data);
  },
  async GetCouponInfo(data) {
    return getCouponInfo(data);
  },
  async GetCouponList(data) {
    return getCouponList(data);
  },
  async SaveCouponInfo(data) {
    return saveCouponInfo(data);
  },
  async SaveCouponStatus(data) {
    return saveCouponStatus(data);
  },
  async SaveCouponOverlay(data) {
    return saveCouponOverlay(data);
  },
  async SaveCouponUsedStatus(data) {
    return saveCouponUsedStatus(data);
  },
  async GetCounponDetailStatus(data) {
    return getCounponDetailStatus(data);
  },
  async ExportCouponDetail(data) {
    return exportCouponDetail(data);
  },
  async AssginCustomerCoupon(data) {
    return assginCustomerCoupon(data);
  },
  async CouponLock(data) {
    return couponLock(data);
  },
  async AssginStudentCoupon(data) {
    return assginStudentCoupon(data);
  },
  async CouponUnlock(data) {
    return couponUnlock(data);
  },
  async SaveAvailable(data) {
    return saveAvailable(data);
  },
  async getCouponDepartmentList(data) {
    return getCouponDepartmentList(data);
  },
  async GetCouponNumber(data) {
    return getCouponNumber(data);
  }
};
