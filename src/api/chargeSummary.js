import axios from "../http";
import Vue from "vue";
import qs from "qs";

// 全国
function getNationwideData(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/order-service/admin/fee-stat/course-area-fee-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 全国-导出
function exportNationwideData(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/order-service/admin/fee-stat/course-area-fee-list-export?${new_data}`,
      {
        params: {
          exportData: 1
        }
      }
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 区域
function getAreaData(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/order-service/admin/fee-stat/course-area-department-fee-list?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 区域-导出
function exportAreaData(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/order-service/admin/fee-stat/course-area-department-fee-list-export?${new_data}`,
      {
        params: {
          exportData: 1
        }
      }
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 校区
function getDepartmentData(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/order-service/admin/fee-stat/course-department-fee-list?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 校区-导出
function exportDepartmentData(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/order-service/admin/fee-stat/course-department-fee-list-export?${new_data}`,
      {
        params: {
          exportData: 1
        }
      }
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 课程
function getCourseData(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/order-service/admin/fee-stat/course-info-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 课程-总计
function getCourseTotalData(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/order-service/admin/fee-stat/course-info-list-total?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 课程-导出
function exportCourseData(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/order-service/admin/fee-stat/course-info-list-export?${new_data}`,
      {
        params: {
          exportData: 1
        }
      }
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function getGoodsList(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/order-service/admin/fee-stat/article-order-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function getGoodsTotal(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/order-service/admin/fee-stat/article-order-total?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function getGoodsRefundList(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/order-service/admin/fee-stat/article-refund-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function getGoodsRefundTotal(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/order-service/admin/fee-stat/article-refund-total?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function getGoodsTransferList(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/order-service/admin/fee-stat/article-transfer-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function getGoodsTransferTotal(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/order-service/admin/fee-stat/article-transfer-total?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 物品销售汇总表
function getGoodsSummaryData(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/order-service/admin/fee-stat/article-fee-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 物品销售汇总表-合计
function getGoodsSummaryTotalData(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/order-service/admin/fee-stat/article-fee-total?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function exportGoodsList(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/order-service/admin/fee-stat/article-order-list-export?${new_data}`,
      {
        params: {
          exportData: 1
        }
      }
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function exportGoodsRefundList(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/order-service/admin/fee-stat/article-refund-list-export?${new_data}`,
      {
        params: {
          exportData: 1
        }
      }
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function exportGoodsTransferList(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/order-service/admin/fee-stat/article-transfer-list-export?${new_data}`,
      {
        params: {
          exportData: 1
        }
      }
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 赛事汇总表
function getMatchFeeList(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/order-service/admin/fee-stat/match-fee-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 赛事汇总合计
function getMatchFeeTotal(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/order-service/admin/fee-stat/match-fee-total?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 赛事明细收费
function getMatchOrderList(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/order-service/admin/fee-stat/match-order-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 赛事收费导出
function getMatchOrderListExport(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/order-service/admin/fee-stat/match-order-list-export?${new_data}`,
      {
        params: {
          exportData: 1
        }
      }
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 赛事明细合计
function getMatchOrderTotal(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/order-service/admin/fee-stat/match-order-total?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 赛事退费列表
function getMatchRefundList(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/order-service/admin/fee-stat/match-refund-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 赛事退费导出
function getMatchRefundListExport(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/order-service/admin/fee-stat/match-refund-list-export?${new_data}`,
      {
        params: {
          exportData: 1
        }
      }
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 赛事退费合计
function getMatchRefundTotal(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/order-service/admin/fee-stat/match-refund-total?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 赛事结转列表
function getMatchTransferList(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/order-service/admin/fee-stat/match-transfer-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 赛事结转导出
function getMatchTransferListExport(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/order-service/admin/fee-stat/match-transfer-list-export?${new_data}`,
      {
        params: {
          exportData: 1
        }
      }
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 赛事结转合计
function getMatchTransferTotal(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/order-service/admin/fee-stat/match-transfer-total?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 教辅包销售汇总表
function getPackageFeeList(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/order-service/admin/teach-aid-package/package-fee-list?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 教辅包汇总表-导出
function packageFeeListExport(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/order-service/admin/teach-aid-package/package-fee-list-export?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 教辅包汇总表合计
function packageFeeTotal(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/order-service/admin/teach-aid-package/package-fee-total?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 教辅包汇总表明细 收费
function packageOrderList(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/order-service/admin/teach-aid-package/package-order-list?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 教辅包汇总表明细-收费-导出
function packageOrderListExport(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/order-service/admin/teach-aid-package/package-order-list-export?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 教辅包销售汇总明细-合计-收费
function packageOrderTotal(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/order-service/admin/teach-aid-package/package-order-total?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 教辅包销售汇总明细 退费
function packageRefundList(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/order-service/admin/teach-aid-package/package-refund-list?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 教辅包销售汇总明细-退费-导出
function packageRefundListExport(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/order-service/admin/teach-aid-package/package-refund-list-export?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 教辅包销售汇总明细-退费-合计
function packageRefundTotal(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/order-service/admin/teach-aid-package/package-refund-total?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 教辅包内容物销售汇总表-列表
function teachAidPackageFeeList(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/order-service/admin/fee-stat/teach-aid-package-fee-list?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 教辅包内容物销售汇总表-导出
function teachAidPackageFeeListExport(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/order-service/admin/fee-stat/teach-aid-package-list-export?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 教辅包内容物销售汇总表-合计
function teachAidPackageFeeTotal(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/order-service/admin/fee-stat/teach-aid-package-fee-total?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 教辅包内容物销售汇总明细-收费
function teachAidPackageOrderList(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/order-service/admin/fee-stat/teach-aid-package-order-list?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 教辅包内容物销售汇总明细-收费-导出
function teachAidPackageOrderListExport(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/order-service/admin/fee-stat/teach-aid-package-order-list-export?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 教辅包内容物销售汇总明细-收费-合计
function teachAidPackageOrderListTotal(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/order-service/admin/fee-stat/teach-aid-package-order-list-total?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 教辅包内容物销售汇总明细-退费-列表
function teachAidPackageRefundList(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/order-service/admin/fee-stat/teach-aid-package-refund-list?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 教辅包内容物销售汇总明细-退费-导出
function teachAidPackageRefundListExport(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/order-service/admin/fee-stat/teach-aid-package-refund-list-export${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 教辅包销售汇总明细-退费-合计
function teachAidPackageRefundTotal(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/order-service/admin/fee-stat/teach-aid-package-refund-total?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
export default {
  async getNationwideData(data) {
    return getNationwideData(data);
  },
  async exportNationwideData(data) {
    return exportNationwideData(data);
  },
  async getAreaData(data) {
    return getAreaData(data);
  },
  async exportAreaData(data) {
    return exportAreaData(data);
  },
  async getDepartmentData(data) {
    return getDepartmentData(data);
  },
  async exportDepartmentData(data) {
    return exportDepartmentData(data);
  },
  async getCourseData(data) {
    return getCourseData(data);
  },
  async exportCourseData(data) {
    return exportCourseData(data);
  },
  async getGoodsList(data) {
    return getGoodsList(data);
  },
  async getGoodsRefundList(data) {
    return getGoodsRefundList(data);
  },
  async getGoodsTransferList(data) {
    return getGoodsTransferList(data);
  },
  async getGoodsTotal(data) {
    return getGoodsTotal(data);
  },
  async getGoodsSummaryData(data) {
    return getGoodsSummaryData(data);
  },
  async getGoodsSummaryTotalData(data) {
    return getGoodsSummaryTotalData(data);
  },
  async getGoodsRefundTotal(data) {
    return getGoodsRefundTotal(data);
  },
  async getGoodsTransferTotal(data) {
    return getGoodsTransferTotal(data);
  },
  async exportGoodsList(data) {
    return exportGoodsList(data);
  },
  async exportGoodsRefundList(data) {
    return exportGoodsRefundList(data);
  },
  async exportGoodsTransferList(data) {
    return exportGoodsTransferList(data);
  },
  async getCourseTotalData(data) {
    return getCourseTotalData(data);
  },
  async getMatchFeeList(data) {
    return getMatchFeeList(data);
  },
  async getMatchFeeTotal(data) {
    return getMatchFeeTotal(data);
  },
  async getMatchOrderList(data) {
    return getMatchOrderList(data);
  },
  async getMatchOrderListExport(data) {
    return getMatchOrderListExport(data);
  },
  async getMatchOrderTotal(data) {
    return getMatchOrderTotal(data);
  },
  async getMatchRefundList(data) {
    return getMatchRefundList(data);
  },
  async getMatchRefundListExport(data) {
    return getMatchRefundListExport(data);
  },
  async getMatchRefundTotal(data) {
    return getMatchRefundTotal(data);
  },
  async getMatchTransferList(data) {
    return getMatchTransferList(data);
  },
  async getMatchTransferListExport(data) {
    return getMatchTransferListExport(data);
  },
  async getMatchTransferTotal(data) {
    return getMatchTransferTotal(data);
  },
  async getPackageFeeList(data) {
    return getPackageFeeList(data);
  },
  async packageFeeListExport(data) {
    return packageFeeListExport(data);
  },
  async packageFeeTotal(data) {
    return packageFeeTotal(data);
  },
  async packageOrderList(data) {
    return packageOrderList(data);
  },
  async packageOrderListExport(data) {
    return packageOrderListExport(data);
  },
  async packageOrderTotal(data) {
    return packageOrderTotal(data);
  },
  async packageRefundList(data) {
    return packageRefundList(data);
  },
  async packageRefundListExport(data) {
    return packageRefundListExport(data);
  },
  async packageRefundTotal(data) {
    return packageRefundTotal(data);
  },
  async teachAidPackageFeeList(data) {
    return teachAidPackageFeeList(data);
  },
  async teachAidPackageFeeListExport(data) {
    return teachAidPackageFeeListExport(data);
  },
  async teachAidPackageFeeTotal(data) {
    return teachAidPackageFeeTotal(data);
  },
  async teachAidPackageOrderList(data) {
    return teachAidPackageOrderList(data);
  },
  async teachAidPackageOrderListExport(data) {
    return teachAidPackageOrderListExport(data);
  },
  async teachAidPackageOrderListTotal(data) {
    return teachAidPackageOrderListTotal(data);
  },
  async teachAidPackageRefundList(data) {
    return teachAidPackageRefundList(data);
  },
  async teachAidPackageRefundListExport(data) {
    return teachAidPackageRefundListExport(data);
  },
  async teachAidPackageRefundTotal(data) {
    return teachAidPackageRefundTotal(data);
  }
};
