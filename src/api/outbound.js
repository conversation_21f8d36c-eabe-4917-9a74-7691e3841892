import axios from "../http";
import Vue from "vue";

// 任务统计图
function getGroupinfo(data) {
  return axios
    .get(`api/aiphone-service/group/info?group_id=${data.group_id}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 任务列表/
function getTaskList(data) {
  return axios
    .get(`/api/aiphone-service/task/list`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 任务组列表/
function getGroupList(data) {
  return axios
    .get(`/api/aiphone-service/group/list`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 话术/
function getSceneList(data) {
  return axios
    .get(`/api/aiphone-service/scene/list`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 线路/
function getDeviceList(data) {
  return axios
    .get(`/api/aiphone-service/device/list`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 创建ai电话/
function getGroupCreate(data) {
  return axios
    .post(`/api/aiphone-service/group/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 未通重播/
function getGroupNoConnectRedo(data) {
  return axios
    .post(`/api/aiphone-service/group/no-connect-redo`, data)

    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 部分重播/
function getGroupPartRedo(data) {
  return axios
    .post(`/api/aiphone-service/group/all-redo`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 任务详情
function getTaskInfo(data) {
  return axios
    .get(`/api/aiphone-service/task/info?task_id=${data.task_id}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 更新任务详情
function taskUpdate(data) {
  return axios
    .post(`/api/aiphone-service/task/update`, data)

    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 导出AI电话
function taskExport(data) {
  return axios
    .get(`/api/aiphone-service/task/export`, {
      params: { ...data, exportData: 1 }
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export default {
  // 任务统计图
  async GetGroupinfo(data) {
    return getGroupinfo(data);
  },

  // 任务列表
  async GetTaskList(data) {
    return getTaskList(data);
  },
  // 任务组列表
  async GetGroupList(data) {
    return getGroupList(data);
  },
  // 话术
  async GetSceneList(data) {
    return getSceneList(data);
  },
  // 线路
  async GetDeviceList(data) {
    return getDeviceList(data);
  },
  // 创建ai电话
  async GetGroupCreate(data) {
    return getGroupCreate(data);
  },
  // 未通重播
  async GetGroupNoConnectRedo(data) {
    return getGroupNoConnectRedo(data);
  },
  // 全部重播
  async GetGroupPartRedo(data) {
    return getGroupPartRedo(data);
  },
  // 任务详情
  async GetTaskInfo(data) {
    return getTaskInfo(data);
  },
  // 任务更新
  async TaskUpdate(data) {
    return taskUpdate(data);
  },
  // 导出AI电话
  async TaskExport(data) {
    return taskExport(data);
  }
};
