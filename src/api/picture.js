import { fetchGet, fetchPost, fetchDel, fetchPatch } from "../fetch";
import qs from "qs";
const api_path = "/api/organization-service/picture";
const category_path = "/api/organization-service/picture-category";

// 创建图片
function createPicture(data) {
  return fetchPost(`${api_path}/create`, data, "");
}

// 删除图片
function deletePicture(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchDel(`${api_path}/delete?${new_data}`);
}

// 图片信息
function getPictureInfo(data) {
  return fetchGet(`${api_path}/info`, {
    params: data
  });
}

// 更新图片
function updatePicture(data) {
  return fetchPatch(`${api_path}/update?picture_id=${data.picture_id}`, data);
}
// 获取图片列表
function getPictureList(data) {
  return fetchGet(`${api_path}/list`, {
    params: data
  });
}

// 创建图片类别
function createPictureModel(data) {
  return fetchPost(`${category_path}/create`, data, "");
}

// 删除图片类别
function deletePictureModel(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchDel(`${category_path}/delete?${new_data}`);
}

// 更新图片类别
function updatePictureModel(data) {
  return fetchPatch(
    `${category_path}/update?category_id=${data.category_id}`,
    data
  );
}

// 获取图片类别列表
function getPictureModelList(data) {
  return fetchGet(`${category_path}/list`, {
    params: data
  });
}
export default {
  createPicture,
  deletePicture,
  getPictureInfo,
  getPictureList,
  createPictureModel,
  deletePictureModel,
  getPictureModelList,
  updatePictureModel,
  updatePicture
};
