import axios from "../http";
import Vue from "vue";

function aliyunWareCategory(data) {
  const config = {
    params: data
  };
  if (data) {
    if (data?.requestType === "MINIGRAMER") {
      config.headers = {
        token: data.token
      };
    }
  }
  return axios
    .get(`/api/market-service/aliyun/config`, config)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export default {
  async AliyunWareCategory(data) {
    return aliyunWareCategory(data);
  }
};
