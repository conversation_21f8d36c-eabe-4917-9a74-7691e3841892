import axios from "../http";
import Vue from "vue";
import qs from "qs";
// 学生列表
function getSchoolServiceClassroomStudentList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/school-service/classroom-student/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}

function getSchoolServiceClassroomStudentClassList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/school-service/classroom-student/class-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 学生状态查询
function getSchoolStudentStatus() {
  return axios
    .get(`/api/school-service/classroom-student/map/status`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 班级列表人数合计
function getStudentTotal(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/school-service/classroom/total-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 班级导出模板下载
function downloadTemplate(data) {
  return axios
    .get(`/api/school-service/classroom/demoDownload`, {
      params: { ...data, exportData: 1 }
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 班级导入
function importTemplate(data) {
  return axios
    .post(`/api/school-service/classroom/import`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return error;
    })
    .finally();
}

// 学员入班导入
function importClassroom(data) {
  return axios
    .post(`/api/school-service/classroom/add-student-import`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return error;
    })
    .finally();
}

// 班级列表
function getSchoolServiceClassroomList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/school-service/classroom/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 班级列表新接口
function getSchoolServiceClassroomListNew(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/school-service/classroom/new-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 班级详情
function getSchoolServiceClassroomInfo(data) {
  return axios
    .get(`/api/school-service/classroom/info?classroom_id=${data.id}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取班级状态
function getSchoolServiceClassroomMapStatus(data) {
  return axios
    .get(`/api/school-service/classroom/map/status`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取班级类型
function getSchoolServiceClassroomMapType(data) {
  return axios
    .get(`/api/school-service/classroom/map/type`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取班级类别
function getSchoolServiceClassroomMapCategory(data) {
  return axios
    .get(`/api/school-service/classroom/map/category`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取班级排课状态
function getSchoolServiceClassroomMapScheduling(data) {
  return axios
    .get(`/api/school-service/classroom/map/scheduling`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 班级导出
function schoolServiceClassroomExcel(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/school-service/classroom/export?${new_data}`, {
      params: { exportData: 1 }
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 批量删除班级
function schoolServiceClassroomDelete(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .delete(`/api/school-service/classroom/delete?${new_data}`)
    .then((response) => {
      if (response.status === 200) {
        Vue.prototype.$message.success("删除成功");
      }
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 新增班级
function getSchoolServiceClassroomCreate(data) {
  return axios
    .post(`/api/school-service/classroom/create`, data)
    .then((response) => {
      if (response.status === 200) {
        Vue.prototype.$message.success("添加成功");
      }
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 班级添加学生
function getSchoolServiceClassroomAddstudent(data) {
  return axios
    .post(`/api/school-service/classroom/add-student`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 班级结业
function getSchoolServiceClassroomFinished(data) {
  return axios
    .post(`/api/school-service/classroom/finished`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 更新班级班主任
function schoolServiceClassroomUpdateHeaderTeacher(data) {
  return axios
    .post(
      `/api/school-service/classroom/update-header-teacher?classroom_id=${data.id}`,
      data
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 更新班级
function getSchoolServiceClassroomUpdate(data) {
  return axios
    .patch(`/api/school-service/classroom/update?classroom_id=${data.id}`, data)
    .then((response) => {
      if (response.status === 200) {
        Vue.prototype.$message.success("修改成功");
      }
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取课程形式
function getSchoolServiceSchedulingMapForm(data) {
  return axios
    .get(`/api/school-service/scheduling/map/form`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 班级进出学生
function shiftStudent(data) {
  return axios
    .post(`/api/school-service/classroom/shift-student`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取课程管理开班规则开班的班级数量
function getClassRoomNum(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/school-service/classroom/course-rule-class-room-num?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 确认结业
function checkClassroomSchedulingStatus(data) {
  return axios
    .post(
      `/api/school-service/classroom/check-classroom-scheduling-status`,
      data
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取老师今日上课时间
function getTeacherTimeList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/school-service/class-time/teacher/time?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export default {
  // 学生列表
  async GetSchoolServiceClassroomStudentList(data) {
    return getSchoolServiceClassroomStudentList(data);
  },
  // 不只显示当前在读班级
  async GetSchoolServiceClassroomStudentClassList(data) {
    return getSchoolServiceClassroomStudentClassList(data);
  },
  // 学生列表
  async getSchoolStudentStatus() {
    return getSchoolStudentStatus();
  },
  // 班级列表
  async GetSchoolServiceClassroomList(data) {
    return getSchoolServiceClassroomList(data);
  },
  // 班级列表新接口
  async GetSchoolServiceClassroomListNew(data) {
    return getSchoolServiceClassroomListNew(data);
  },
  // 班级详情
  async GetSchoolServiceClassroomInfo(data) {
    return getSchoolServiceClassroomInfo(data);
  },

  // 获取班级类别
  async GetSchoolServiceClassroomMapCategory(data) {
    return getSchoolServiceClassroomMapCategory(data);
  },
  // 获取班级状态
  async GetSchoolServiceClassroomMapStatus(data) {
    return getSchoolServiceClassroomMapStatus(data);
  },
  // 获取班级类型
  async GetSchoolServiceClassroomMapType(data) {
    return getSchoolServiceClassroomMapType(data);
  },

  // 删除班级
  async SchoolServiceClassroomDelete(data) {
    return schoolServiceClassroomDelete(data);
  },

  // 导出
  async SchoolServiceClassroomExcel(data) {
    return schoolServiceClassroomExcel(data);
  },
  // 删除班级
  async schoolServiceClassroomDelete(data) {
    return schoolServiceClassroomDelete(data);
  },
  // 新增班级
  async GetSchoolServiceClassroomCreate(data) {
    return getSchoolServiceClassroomCreate(data);
  },

  // 班级结业
  async GetSchoolServiceClassroomFinished(data) {
    return getSchoolServiceClassroomFinished(data);
  },
  // 班级添加学生
  async GetSchoolServiceClassroomAddstudent(data) {
    return getSchoolServiceClassroomAddstudent(data);
  },
  // 更新班级班主任
  async SchoolServiceClassroomUpdateHeaderTeacher(data) {
    return schoolServiceClassroomUpdateHeaderTeacher(data);
  },
  // 更新班级
  async GetSchoolServiceClassroomUpdate(data) {
    return getSchoolServiceClassroomUpdate(data);
  },

  // 获取课程形式
  async GetSchoolServiceSchedulingMapForm(data) {
    return getSchoolServiceSchedulingMapForm(data);
  },
  //
  // 获取班级排课状态
  async GetSchoolServiceClassroomMapScheduling(data) {
    return getSchoolServiceClassroomMapScheduling(data);
  },
  // 班级进出班
  async ShiftStudent(data) {
    return shiftStudent(data);
  },
  async downloadTemplate(data) {
    return downloadTemplate(data);
  },
  async importTemplate(data) {
    return importTemplate(data);
  },
  async importClassroom(data) {
    return importClassroom(data);
  },
  getClassRoomNum,
  getStudentTotal,
  checkClassroomSchedulingStatus,
  getTeacherTimeList
};
