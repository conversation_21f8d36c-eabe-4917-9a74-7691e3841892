import axios from "../http";
import Vue from "vue";

function login(data) {
  return axios.post("/api/login/login", data).then((response) => {
    return response;
  });
}

function refreshToken(data) {
  return axios
    .get("/api/login/refresh-token", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function corpwechatLogin(data) {
  return axios
    .get(`/api/login/corpwechat-login?corp_user_id=${data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function sendSms(data) {
  return axios
    .post(`/api/login/sms`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function editOldPassword(data) {
  return axios
    .post(`/api/login/origin-edit-password`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function resetPassword(data) {
  return axios
    .post(`/api/login/forget-password-reset`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
export default {
  async Login(data) {
    return login(data);
  },
  async refreshToken(data) {
    return refreshToken(data);
  },
  async corpwechatLogin(data) {
    return corpwechatLogin(data);
  },
  async sendSms(data) {
    return sendSms(data);
  },
  async editOldPassword(data) {
    return editOldPassword(data);
  },
  async resetPassword(data) {
    return resetPassword(data);
  }
};
