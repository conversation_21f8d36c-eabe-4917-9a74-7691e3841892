import axios from "../http";
import Vue from "vue";
import qs from "qs";
const fetch = require("../fetch");
// 发起合同申请
function contractApply(data) {
  return axios
    .post(`/api/order-service/admin/contract/apply`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 企业列表
function companyList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/order-service/admin/contract/organization-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 企业详情
function companyDetail(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/order-service/admin/contract/organization-info?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 企业新增
function companyAdd(data) {
  return axios
    .post(`/api/order-service/admin/contract/organization-add`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 企业绑定校区
function companyBind(data) {
  return axios
    .post(`/api/order-service/admin/contract/organization-bind`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取模版列表
function templateList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/order-service/admin/contract/template-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 模版绑定收费类型
function templateBind(data) {
  return axios
    .post(`/api/order-service/admin/contract/template-bind`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取模版预览
function templatePreview(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/order-service/admin/contract/template-web?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取合同预保存信息
function contractInfo(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/order-service/admin/contract/pre-save-info?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 合同勾选校验
function contractCheck(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/order-service/admin/contract/check?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 合同列表
function contractList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/order-service/admin/contract/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    });
}
// 合同下载
function contractDownload(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/order-service/admin/contract/download?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    });
}
// 导出合同列表
function contractExport(data) {
  return fetch.fetchExport(`/api/order-service/admin/contract/export`, data);
}

// 模版修改
function templateUpdate(data) {
  return axios
    .post(`/api/order-service/admin/contract/template-update`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取电子签模版校区
function contractDepartments(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/order-service/admin/contract/departments?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    });
}
export default {
  async ContractApply(data) {
    return contractApply(data);
  },
  async CompanyList(data) {
    return companyList(data);
  },
  async CompanyDetail(data) {
    return companyDetail(data);
  },
  async CompanyAdd(data) {
    return companyAdd(data);
  },
  async CompanyBind(data) {
    return companyBind(data);
  },
  async TemplateList(data) {
    return templateList(data);
  },
  async TemplateBind(data) {
    return templateBind(data);
  },
  async TemplatePreview(data) {
    return templatePreview(data);
  },
  async ContractInfo(data) {
    return contractInfo(data);
  },
  async ContractCheck(data) {
    return contractCheck(data);
  },
  async ContractList(data) {
    return contractList(data);
  },
  async ContractDownload(data) {
    return contractDownload(data);
  },
  async ContractExport(data) {
    return contractExport(data);
  },
  async TemplateUpdate(data) {
    return templateUpdate(data);
  },
  async ContractDepartments(data) {
    return contractDepartments(data);
  }
};
