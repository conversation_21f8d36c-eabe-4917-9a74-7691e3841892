import { fetchGet, fetchPost, fetchDel, fetchPatch } from "../fetch";

// 新增权限
function createDefaultPermission(data) {
  return fetchPost("/api/permission-service/permission/default/create", data);
}
// 修改权限
function updateDefaultPermission(data) {
  return fetchPatch(
    `/api/permission-service/permission/default/update?default_permission_id=${data.id}`,
    data
  );
}
// 删除权限
function delDefaultPermission(data) {
  return fetchDel(
    `/api/permission-service/permission/default/delete?default_permission_id=${data}`
  );
}
// 权限详情
function infoDefaultPermission(data) {
  return fetchGet(
    `/api/permission-service/permission/default/info?default_permission_id=${data}`
  );
}
// 权限列表
function getDefaultPermission(data) {
  return fetchGet("/api/permission-service/permission/default/list", {
    params: data
  });
}
export default {
  createDefaultPermission,
  updateDefaultPermission,
  delDefaultPermission,
  getDefaultPermission,
  infoDefaultPermission
};
