import axios from "../http";
import Vue from "vue";
import qs from "qs";
const api_path = `/api/school-service/audition`;
const fetch = require("../fetch");

// 试听列表
function getSchoolServiceAuditionList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`${api_path}/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    });
}

// 学生试听列表
function getSchoolServiceAuditionStudent(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`${api_path}/student?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取试听状态
function getSchoolServiceAuditionMapStatus(data) {
  return axios
    .get(`/api/school-service/audition/map/status`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 新开班试听
function createNewClassAudition(data) {
  return axios
    .post(`/api/school-service/audition/new-classroom`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error);
    })
    .finally();
}
// 跟班试听
function addStudent(data) {
  return axios
    .post(`/api/school-service/scheduling/student/add-audition`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}
// 获取操作日志列表
function getOperationList(data) {
  return axios
    .get(`/api/report-center-service/admin/operation-log/list`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取操作日志列表
function getOperationTypeList(data) {
  return axios
    .get(`/api/report-center-service/admin/operation-log/operation-type-list`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 导出
function operationLogExport(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/report-center-service/admin/operation-log/export?${new_data}`,
    { params: { exportData: 1 } }
  );
}
// 试听明细导出
function auditionDetailExport(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/school-service/audition/detail-export?${new_data}`
  );
}

// 试听明细列表
function auditionDetailList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(`/api/school-service/audition/detail-list?${new_data}`);
}

// 试听明细导出状态
function auditionExportRolling(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/school-service/audition/export-rolling?${new_data}`
  );
}

// 试听明细文件下载
function auditionFileDownload(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/school-service/audition/file-download?${new_data}`
  );
}

export default {
  // 试听列表
  async GetSchoolServiceAuditionList(data) {
    return getSchoolServiceAuditionList(data);
  },

  // 学生试听列表
  async GetSchoolServiceAuditionStudent(data) {
    return getSchoolServiceAuditionStudent(data);
  },

  // 获取试听状态
  async GetSchoolServiceAuditionMapStatus(data) {
    return getSchoolServiceAuditionMapStatus(data);
  },
  async createNewClassAudition(data) {
    return createNewClassAudition(data);
  },
  async addStudent(data) {
    return addStudent(data);
  },
  async getOperationList(data) {
    return getOperationList(data);
  },
  async operationLogExport(data) {
    return operationLogExport(data);
  },
  async auditionDetailExport(data) {
    return auditionDetailExport(data);
  },
  async auditionDetailList(data) {
    return auditionDetailList(data);
  },
  async auditionExportRolling(data) {
    return auditionExportRolling(data);
  },
  async auditionFileDownload(data) {
    return auditionFileDownload(data);
  },
  async getOperationTypeList(data) {
    return getOperationTypeList(data);
  }
};
