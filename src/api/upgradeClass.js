import axios from "../http";
import Vue from "vue";
import qs from "qs";

// 升班列表
function getCoursePathList(data) {
  return axios
    .get(`/api/course-service/course-path/list`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 创建升班关系
function setCoursePathUpdate(data) {
  return axios
    .post(`/api/course-service/course-path/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
function upCoursePathUpdate(data) {
  return axios
    .patch(
      `/api/course-service/course-path/update?course_path_id=${data.id}`,
      data
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function delCoursePathUpdate(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .delete(`/api/course-service/course-path/delete?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function exportCoursePathUpdate(data) {
  return axios
    .get(`/api/course-service/course-path/export`, {
      params: {
        ...data,
        exportData: 1
      }
    })

    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
export default {
  async GetCoursePathList(data) {
    return getCoursePathList(data);
  },
  async SetCoursePathUpdate(data) {
    return setCoursePathUpdate(data);
  },
  async DelCoursePathUpdate(data) {
    return delCoursePathUpdate(data);
  },
  async UpCoursePathUpdate(data) {
    return upCoursePathUpdate(data);
  },
  async ExportCoursePathUpdate(data) {
    return exportCoursePathUpdate(data);
  }
};
