import axios from "../http";
import Vue from "vue";
const fetch = require("../fetch");

// 财务锁账 - list
function getFinanceLockList(data) {
  return axios
    .get(`/api/system-service/finance-range/list`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 校区所有的锁定状态锁账周期列表;
function getLockDateList(data) {
  return fetch.fetchGet("/api/system-service/finance-department/lock-list", {
    params: data
  });
}
// Add
function addFinanceLockData(data) {
  return axios
    .post(`/api/system-service/finance-range/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

//  info
function getFinanceLockInfo(data) {
  return axios
    .get(`/api/system-service/finance-range/info`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// update
function updateFinanceLock(data) {
  return axios
    .patch(
      `/api/system-service/finance-range/update?finance_range_id=${data.id}`,
      data
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// updateLockStatus
function updateLockStatus(data) {
  return axios
    .patch(
      `/api/system-service/finance-range/save-status?finance_range_id=${data.id}`,
      data
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// delete
function delFinanceLock(data) {
  return axios
    .delete(
      `/api/system-service/finance-range/delete?finance_range_id=${data.id}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

//  bindSchool - add&update
function updateFinanceLockSchool(data) {
  return axios
    .post(`/api/system-service/finance-department/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

//  bindSchool - list
function getBindSchoolList(data) {
  return axios
    .get(`/api/system-service/finance-department/list`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export default {
  async GetFinanceLockList(data) {
    return getFinanceLockList(data);
  },
  getLockDateList,
  async AddFinanceLockData(data) {
    return addFinanceLockData(data);
  },
  async GetFinanceLockInfo(data) {
    return getFinanceLockInfo(data);
  },
  async UpdateFinanceLock(data) {
    return updateFinanceLock(data);
  },
  async UpdateLockStatus(data) {
    return updateLockStatus(data);
  },
  async DelFinanceLock(data) {
    return delFinanceLock(data);
  },
  async UpdateFinanceLockSchool(data) {
    return updateFinanceLockSchool(data);
  },
  async GetBindSchoolList(data) {
    return getBindSchoolList(data);
  }
};
