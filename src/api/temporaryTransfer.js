// 临调接口
import axios from "../http";
import Vue from "vue";

// 排课临调列表
function getTemporaryTransferList(data) {
  return axios
    .get(`/api/school-service/scheduling-shift/list`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 临调撤回
function temporaryRollback(data) {
  return axios
    .post(`/api/school-service/scheduling-shift/rollback`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 临调
function temporaryShift(data) {
  return axios
    .post(`/api/school-service/scheduling-shift/shift`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
export default {
  async getTemporaryTransferList(data) {
    return getTemporaryTransferList(data);
  },
  async temporaryRollback(data) {
    return temporaryRollback(data);
  },
  async temporaryShift(data) {
    return temporaryShift(data);
  }
};
