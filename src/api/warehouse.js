import qs from "qs";
import { fetchGet, fetchPost, fetchDel } from "../fetch";

// 仓库类型列表
function getWarehouseType(data) {
  return fetchGet(`/api/course-service/article-bank/category-list`, {
    params: data
  });
}

// 仓库列表
function getWarehouseList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/course-service/article-bank/list?${new_data}`);
}

// 新增
function addWarehouse(data) {
  return fetchPost(`/api/course-service/article-bank/create`, data, "");
}

// info
function getWarehouseInfo(data) {
  return fetchGet(`/api/course-service/article-bank/info`, {
    params: data
  });
}

function updateWarehouse(data) {
  return fetchPost(`/api/course-service/article-bank/save`, data, "");
}

// 删除
function delWarehouse(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchDel(`/api/course-service/article-bank/remove?${new_data}`);
}

// 库存查询列表
function getInventoryQueryList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });

  return fetchGet(`/api/course-service/article-bank-amount/list?${new_data}`);
}

// 库存查询导出
function exportInventoryQuery(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/course-service/article-bank-amount/export?${new_data}`,
    {
      params: { exportData: 1 }
    }
  );
}
// 库存查询查看物品库存和库存数量
function getInventoryQueryNum(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/course-service/article-bank-amount/article-bank-num-list?${new_data}`
  );
}
// 库存查询查看物品库存和库存数量合计
function getInventoryQuerySumNum(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/course-service/article-bank-amount/article-bank-num-total?${new_data}`
  );
}
// 库存变动列表
function getInventoryChangeList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/course-service/article-bank-amount-log/change-list?${new_data}`
  );
}
// 库存变动列表合计
function getInventoryChangeListTotalNum(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/course-service/article-bank-amount-log/change-list-total?${new_data}`
  );
}
// 库存变动列表导出
function exportInventoryChange(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/course-service/article-bank-amount-log/change-list-export?${new_data}`,
    {
      params: { exportData: 1 }
    }
  );
}
// 进出库查询列表
function getInventoryEnterOrOuterList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });

  return fetchGet(
    `/api/course-service/enter-leave-stock/info-list?${new_data}`
  );
}
// 进出库查询列表导出
function exportInventoryEnterOrOuter(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/course-service/enter-leave-stock/info-list-export?${new_data}`,
    {
      params: { exportData: 1 }
    }
  );
}
// 操作类型
function getCategory(data) {
  // const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/course-service/enter-leave-stock/category`, {
    params: data
  });
}

// 盘点列表
function getTakeStockList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/course-service/article-bank/article-list?${new_data}`);
}

// 盘点创建
function createTakeStock(data) {
  return fetchPost(
    `/api/course-service/enter-leave-stock/stock-take/create`,
    data,
    ""
  );
}

export default {
  getWarehouseType,
  async getWarehouseList(data) {
    return getWarehouseList(data);
  },
  addWarehouse,
  getWarehouseInfo,
  updateWarehouse,
  delWarehouse,
  getInventoryQueryList,
  exportInventoryQuery,
  getInventoryQueryNum,
  getInventoryChangeList,
  exportInventoryChange,
  getInventoryQuerySumNum,
  getInventoryChangeListTotalNum,
  getInventoryEnterOrOuterList,
  exportInventoryEnterOrOuter,
  getCategory,
  getTakeStockList,
  createTakeStock
};
