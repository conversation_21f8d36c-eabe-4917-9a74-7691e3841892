import axios from "../http";
import Vue from "vue";

// 获取库存列表
function getWmsList(data) {
  return axios
    .get(`/api/course-service/enter-leave-stock/list`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取当前仓库的物品
function getWarehouseGoods(data) {
  return axios
    .get(`/api/course-service/enter-leave-stock/article-bank-list`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取当前物品在当前仓库下的数量
function getCurrentRepertory(data) {
  return axios
    .get(`/api/course-service/article-bank-amount/info`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取当前仓库的可退领人
function getUsePersonList(data) {
  return axios
    .get(`/api/course-service/enter-leave-stock/use-category-list`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取当前仓库的可退领数量
function getWithDrawData(data) {
  return axios
    .get(`/api/course-service/enter-leave-stock/use-withdrawal-category-num`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取相关物品信息
function getWmsInfo(data) {
  return axios
    .get(`/api/course-service/enter-leave-stock/info`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 物品变动 - 进货
function addGoods(data) {
  return axios
    .post(`/api/course-service/enter-leave-stock/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 物品变动 - 退货
function refundAddGoods(data) {
  return axios
    .post(`/api/course-service/enter-leave-stock/refund/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 物品变动 - 调拨
function dialAddGoods(data) {
  return axios
    .post(`/api/course-service/enter-leave-stock/dial/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 物品变动 - 报损
function damageAddGoods(data) {
  return axios
    .post(`/api/course-service/enter-leave-stock/damage/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 物品变动 - 领用
function useAddGoods(data) {
  return axios
    .post(`/api/course-service/enter-leave-stock/use/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 物品变动 - 退领
function withdrawalAddGoods(data) {
  return axios
    .post(`/api/course-service/enter-leave-stock/withdrawal/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 物品变动 - 库存变动
function adjustAddGoods(data) {
  return axios
    .post(`/api/course-service/enter-leave-stock/adjust/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 进货 - 修改
function updateWmsData(data) {
  return axios
    .post(`/api/course-service/enter-leave-stock/save`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 退货 - 修改
function refundUpdateGoods(data) {
  return axios
    .post(`/api/course-service/enter-leave-stock/refund/save`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 调拨 - 修改
function dialUpdateGoods(data) {
  return axios
    .post(`/api/course-service/enter-leave-stock/dial/save`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 报损 - 修改
function damageUpdateGoods(data) {
  return axios
    .post(`/api/course-service/enter-leave-stock/damage/save`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 领用 - 修改
function useUpdateGoods(data) {
  return axios
    .post(`/api/course-service/enter-leave-stock/use/save`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 退领 - 修改
function withdrawalUpdateGoods(data) {
  return axios
    .post(`/api/course-service/enter-leave-stock/withdrawal/save`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 库存变动 - 修改
function adjustUpdateGoods(data) {
  return axios
    .post(`/api/course-service/enter-leave-stock/adjust/save`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 删除 - 进货
function delWmsData(data) {
  return axios
    .delete(`/api/course-service/enter-leave-stock/remove`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 删除 - 退货
function delRefundData(data) {
  return axios
    .delete(`/api/course-service/enter-leave-stock/refund/remove`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 删除 - 调拨
function delDialData(data) {
  return axios
    .delete(`/api/course-service/enter-leave-stock/dial/remove`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 删除 - 报损
function delDamageData(data) {
  return axios
    .delete(`/api/course-service/enter-leave-stock/damage/remove`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 删除 - 领用
function delUseData(data) {
  return axios
    .delete(`/api/course-service/enter-leave-stock/use/remove`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 删除 - 退领
function delWithdrawalData(data) {
  return axios
    .delete(`/api/course-service/enter-leave-stock/withdrawal/remove`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 删除 - 库存变动
function delAdjustData(data) {
  return axios
    .delete(`/api/course-service/enter-leave-stock/adjust/remove`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export default {
  async addGoods(data) {
    return addGoods(data);
  },
  async getWarehouseGoods(data) {
    return getWarehouseGoods(data);
  },
  async getCurrentRepertory(data) {
    return getCurrentRepertory(data);
  },
  async refundAddGoods(data) {
    return refundAddGoods(data);
  },
  async dialAddGoods(data) {
    return dialAddGoods(data);
  },
  async getWmsList(data) {
    return getWmsList(data);
  },
  async damageAddGoods(data) {
    return damageAddGoods(data);
  },
  async useAddGoods(data) {
    return useAddGoods(data);
  },
  async withdrawalAddGoods(data) {
    return withdrawalAddGoods(data);
  },
  async adjustAddGoods(data) {
    return adjustAddGoods(data);
  },
  async getWmsInfo(data) {
    return getWmsInfo(data);
  },
  async updateWmsData(data) {
    return updateWmsData(data);
  },
  async refundUpdateGoods(data) {
    return refundUpdateGoods(data);
  },
  async dialUpdateGoods(data) {
    return dialUpdateGoods(data);
  },
  async damageUpdateGoods(data) {
    return damageUpdateGoods(data);
  },
  async useUpdateGoods(data) {
    return useUpdateGoods(data);
  },
  async withdrawalUpdateGoods(data) {
    return withdrawalUpdateGoods(data);
  },
  async adjustUpdateGoods(data) {
    return adjustUpdateGoods(data);
  },
  async delWmsData(data) {
    return delWmsData(data);
  },
  async delRefundData(data) {
    return delRefundData(data);
  },
  async delDialData(data) {
    return delDialData(data);
  },
  async delDamageData(data) {
    return delDamageData(data);
  },
  async delUseData(data) {
    return delUseData(data);
  },
  async delAdjustData(data) {
    return delAdjustData(data);
  },
  async delWithdrawalData(data) {
    return delWithdrawalData(data);
  },
  async getUsePersonList(data) {
    return getUsePersonList(data);
  },
  async getWithDrawData(data) {
    return getWithDrawData(data);
  }
};
