import axios from "../http";
import Vue from "vue";
import qs from "qs";

// 开班班级列表
export const getFeedbackDetail = (data) => {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/school-service/miniprogram/feedback/detail?${new_data}`, {
      params: data
    })
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
};
export function feedbackCancel(data) {
  return axios
    .post(`/api/school-service/miniprogram/feedback/cancel`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export const studentFeedbackInfo = (data) => {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/web/questionnaire-web-service/studentFeedbackInfo?${new_data}`, {
      params: data
    })
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
};
