import axios from "../http";
import Vue from "vue";
import qs from "qs";

function getCouponList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/coupon-service/plan/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function getCustomList(data) {
  return axios
    .get(`/api/coupon-service/plan/custom-list`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function getCouponInfo(data) {
  return axios
    .get(`/api/coupon-service/plan/info`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function addCouponData(data) {
  return axios
    .post(`/api/coupon-service/plan/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function addCustomData(data) {
  return axios
    .post(`/api/coupon-service/plan/custom-add`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function updateCouponData(data) {
  return axios
    .post(`/api/coupon-service/plan/update`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function deleteCoupon(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .delete(`/api/coupon-service/plan/delete?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function deleteCustomData(data) {
  return axios
    .delete(`/api/coupon-service/plan/custom-delete/?department_id=${data.id}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
function getCourseSpecificationsInfo(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/course-service/course-price/course-specifications?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export default {
  async GetCouponList(data) {
    return getCouponList(data);
  },
  async GetCustomList(data) {
    return getCustomList(data);
  },
  async AddCouponData(data) {
    return addCouponData(data);
  },
  async AddCustomData(data) {
    return addCustomData(data);
  },
  async GetCouponInfo(data) {
    return getCouponInfo(data);
  },
  async UpdateCouponData(data) {
    return updateCouponData(data);
  },
  async DeleteCoupon(data) {
    return deleteCoupon(data);
  },
  async DeleteCustomData(data) {
    return deleteCustomData(data);
  },
  async getCourseSpecificationsInfo(data) {
    return getCourseSpecificationsInfo(data);
  }
};
