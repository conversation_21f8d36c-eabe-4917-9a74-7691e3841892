import axios from "../http";
import Vue from "vue";
import qs from "qs";

// 获取用户的课程剩余数量和金额权限列表
export function restCourseWebShowList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/questionnaire-service/admin/miniProgram/restCourseWebShow/list?${new_data}`
    )
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取用户的课程剩余数量和金额修改权限
export function restCourseWebShowEdit(data) {
  return axios
    .post(
      `/api/questionnaire-service/admin/miniProgram/restCourseWebShow/edit`,
      data
    )
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取用户的课程剩余数量和金额删除权限
export function restCourseWebShowDelete(data) {
  return axios
    .post(
      `/api/questionnaire-service/admin/miniProgram/restCourseWebShow/delete`,
      data
    )
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取用户的课程剩余数量和金额添加权限
export function restCourseWebShowAdd(data) {
  return axios
    .post(
      `/api/questionnaire-service/admin/miniProgram/restCourseWebShow/add`,
      data
    )
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
