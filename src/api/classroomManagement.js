import axios from "../http";
import Vue from "vue";
import qs from "qs";

// 获取教室列表
function getSchoolroomList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/school-service/schoolroom/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取教室信息
function getSchoolroomInfo(data) {
  return axios
    .get(`/api/school-service/schoolroom/info?school_room_id=${data.id}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 新增教室
function getSchoolroomCreate(data) {
  return axios
    .post(`/api/school-service/schoolroom/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 修改教室
function schoolroomUpdate(data) {
  return axios
    .patch(
      `/api/school-service/schoolroom/update?school_room_id=${data.id}`,
      data
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 删除教室
function schoolroomDelete(data) {
  return axios
    .delete(`/api/school-service/schoolroom/delete?school_room_id=${data.id}`)
    .then((response) => {
      if (response.status === 200) {
        Vue.prototype.$message.success("删除成功");
      }
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取等级列表
function getNwpLevelList() {
  return axios
    .get("/api/school-service/nwp-level/list")
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 班级移除学生
function removeStudents(data) {
  return axios
    .post("/api/school-service/classroom/remove-student", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 班级添加学生
function addStudents(data) {
  return axios
    .post("/api/school-service/classroom/add-student", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 班级调班
function changeStudentClass(data) {
  return axios
    .post("/api/school-service/classroom/shift-student", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
export default {
  // 获取教室列表
  async GetSchoolroomList(data) {
    return getSchoolroomList(data);
  },
  // 获取教室信息
  async GetSchoolroomInfo(data) {
    return getSchoolroomInfo(data);
  },
  // 新增教室
  async GetSchoolroomCreate(data) {
    return getSchoolroomCreate(data);
  },
  // 修改教室
  async SchoolroomUpdate(data) {
    return schoolroomUpdate(data);
  },
  // 删除教室
  async SchoolroomDelete(data) {
    return schoolroomDelete(data);
  },
  // 等级
  async GetNwpLevelList(data) {
    return getNwpLevelList(data);
  },
  async addStudents(data) {
    return addStudents(data);
  },
  async removeStudents(data) {
    return removeStudents(data);
  },
  async changeStudentClass(data) {
    return changeStudentClass(data);
  }
};
