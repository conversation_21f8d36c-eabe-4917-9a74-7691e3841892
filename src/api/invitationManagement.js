import axios from "../http";
import Vue from "vue";
import qs from "qs";
// 获取邀约查询列表
function getInvitationList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/market-service/invitation/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 更新邀约查询
function getInvitationUpdate(data) {
  return axios
    .patch(
      `/api/market-service/invitation/update?invitation_id=${data.id}`,
      data
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 导出，线索导出
function exportExcel(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });

  return axios
    .get(`/api/market-service/invitation/export?${new_data}`, {
      params: { exportData: 1 }
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
export default {
  // 获取岗位列表
  async GetInvitationList(data) {
    return getInvitationList(data);
  },
  // 更新邀约查询
  async GetInvitationUpdate(data) {
    return getInvitationUpdate(data);
  },
  // 导出
  async ExportExcel(data) {
    return exportExcel(data);
  }
};
