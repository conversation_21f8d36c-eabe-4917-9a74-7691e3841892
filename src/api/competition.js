import axios from "../http";
import Vue from "vue";
import qs from "qs";

// 赛事详情
function matchDetail(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/questionnaire-service/admin/match/detail?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 创建赛事
function matchCreate(data) {
  return axios
    .post(`/api/questionnaire-service/admin/match/create`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 赛事列表
function matchList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/questionnaire-service/admin/match/list?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 添加学生
function matchStudentAdd(data) {
  return axios
    .post(`/api/questionnaire-service/admin/match/student/add`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 学生列表
function matchStudentList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/questionnaire-service/admin/match/student/list?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 学生状态更新
function matchStudentUpdate(data) {
  return axios
    .post(`/api/questionnaire-service/admin/match/student/update`, data)
    .then((response) => {
      return response.data;
    });
}
function matchStudentUpdateStatus(data) {
  return axios
    .post(`/api/questionnaire-service/admin/match/student/update/status`, data)
    .then((response) => {
      return response.data;
    });
}
// 删除自定义字段
function matchCustomizeDel(data) {
  return axios
    .post(`/api/questionnaire-service/admin/match/customize/del`, data)
    .then((response) => {
      return response.data;
    });
}
// 赛事更新
function matchUpdate(data) {
  return axios
    .post(`/api/questionnaire-service/admin/match/update`, data)
    .then((response) => {
      return response.data;
    });
}
// 赛事状态更新
function matchUpdateStatus(data) {
  return axios
    .post(`/api/questionnaire-service/admin/match/update/status`, data)
    .then((response) => {
      return response.data;
    });
}
// 赛事删除
function matchDelete(data) {
  return axios
    .post(`/api/questionnaire-service/admin/match/del`, data)
    .then((response) => {
      return response.data;
    });
}
// 赛事导出
function matchExport(data) {
  return axios
    .post(`/api/questionnaire-service/admin/match/export`, data)
    .then((response) => {
      return response.data;
    });
}
// 新增报名学生
function matchStudentCustomize(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/questionnaire-service/admin/match/student/customize?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 学生详情
function studentDetail(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/questionnaire-service/admin/match/student/detail?${new_data}`)
    .then((response) => {
      return response.data;
    });
}
export default {
  async matchDetail(data) {
    return matchDetail(data);
  },
  async matchCreate(data) {
    return matchCreate(data);
  },
  async matchList(data) {
    return matchList(data);
  },
  async matchStudentAdd(data) {
    return matchStudentAdd(data);
  },
  async matchStudentList(data) {
    return matchStudentList(data);
  },
  async matchStudentUpdateStatus(data) {
    return matchStudentUpdateStatus(data);
  },
  async matchUpdate(data) {
    return matchUpdate(data);
  },
  async matchUpdateStatus(data) {
    return matchUpdateStatus(data);
  },
  async matchDelete(data) {
    return matchDelete(data);
  },
  async matchExport(data) {
    return matchExport(data);
  },
  async matchStudentCustomize(data) {
    return matchStudentCustomize(data);
  },
  async studentDetail(data) {
    return studentDetail(data);
  },
  async matchStudentUpdate(data) {
    return matchStudentUpdate(data);
  },
  async matchCustomizeDel(data) {
    return matchCustomizeDel(data);
  }
};
