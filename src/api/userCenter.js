import axios from "../http";
import Vue from "vue";
import qs from "qs";

// 流程审批-个人中心-审批记录
function approveRecord(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/workstream-service/approve/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.message);
    })
    .finally();
}

// 流程审批-个人中心-审批导出
function exportExcel(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });

  return axios
    .get(`/api/workstream-service/approve/export?${new_data}`, {
      params: { exportData: 1 }
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 审批流 信息和系统信息
function getWorkflowInfo(data) {
  return axios
    .get(`/api/workstream-service/approve/detail`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 审批流最新修改记录基本信息
function getLastUpdateRecord(data) {
  return axios
    .get(`/api/workflow-service/approval/last-business-info`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 审批流所有修改记录
function getUpdateRecord(data) {
  return axios
    .get(`/api/workflow-service/approval/all-business-info`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 提交审批节点意见
function createWorkflowSuggest(data) {
  return axios
    .post(`/api/workstream-service/reviewer/update`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.message);
    })
    .finally();
}

// 撤销审批
function revocateWorkflow(data) {
  return axios
    .post(`/api/workstream-service/approve/revoke`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.message);
    })
    .finally();
}

// 已读
function setAlreadyRead(data) {
  return axios
    .post(`/api/workflow-service/approval/cc-read`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 催办
function pushTo(data) {
  return axios
    .post(`/api/workstream-service/reviewer/urge`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 审批历史信息
function getHistoryInfo(data) {
  return axios
    .get(`/api/workflow-service/approval/history-info`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取此人在这个节点的状态
function getMyApplication(data) {
  return axios
    .get(`/api/workflow-service/approval/my-application`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取配置信息列表
function getDispositionList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/taskpool-service/admin/common/config-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取配置信息详情
function getDispositionInfo(data) {
  return axios
    .get(`/api/taskpool-service/admin/common/config-info`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 创建配置项
function addDispositionData(data) {
  return axios
    .post(`/api/taskpool-service/admin/common/config-create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 创建配置项
function updateDispositionData(data) {
  return axios
    .post(`/api/taskpool-service/admin/common/config-update`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 删除
function delDispositionData(data) {
  return axios
    .delete(`/api/taskpool-service/admin/common/config-delete`, {
      data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export default {
  async approveRecord(data) {
    return approveRecord(data);
  },
  async exportExcel(data) {
    return exportExcel(data);
  },
  async getWorkflowInfo(data) {
    return getWorkflowInfo(data);
  },
  async getUpdateRecord(data) {
    return getUpdateRecord(data);
  },
  async getLastUpdateRecord(data) {
    return getLastUpdateRecord(data);
  },
  async createWorkflowSuggest(data) {
    return createWorkflowSuggest(data);
  },
  async revocateWorkflow(data) {
    return revocateWorkflow(data);
  },
  async setAlreadyRead(data) {
    return setAlreadyRead(data);
  },
  async pushTo(data) {
    return pushTo(data);
  },
  async getHistoryInfo(data) {
    return getHistoryInfo(data);
  },
  async getMyApplication(data) {
    return getMyApplication(data);
  },

  async getDispositionList(data) {
    return getDispositionList(data);
  },
  async getDispositionInfo(data) {
    return getDispositionInfo(data);
  },
  async addDispositionData(data) {
    return addDispositionData(data);
  },
  async updateDispositionData(data) {
    return updateDispositionData(data);
  },
  async delDispositionData(data) {
    return delDispositionData(data);
  }
};
