import axios from "../http";
import Vue from "vue";
import qs from "qs";

// 全国
function shiftPoolList(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/school-service/classroom/shift-pool-list?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
function shiftAdjustStudent(data) {
  return axios
    .post(`/api/school-service/classroom/shift-adjust-student`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return error;
    })
    .finally();
}
function cancelShiftPool(data) {
  return axios
    .post(`/api/school-service/classroom/cancel-shift-pool`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return error;
    })
    .finally();
}
export default {
  async shiftPoolList(data) {
    return shiftPoolList(data);
  },
  async shiftAdjustStudent(data) {
    return shiftAdjustStudent(data);
  },
  async cancelShiftPool(data) {
    return cancelShiftPool(data);
  }
};
