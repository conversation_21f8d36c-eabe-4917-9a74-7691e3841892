// import { fetchGet, fetchDel, fetchPost, fetchPatch } from "../fetch";
import qs from "qs";
import axios from "../http";
import Vue from "vue";

// 导入数据
function importData(data) {
  return axios
    .post(
      `/api/report-center-service/admin/compensation-structure/import-data`,
      data
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return error;
    })
    .finally();
}

// 数据加锁
function lockData(data) {
  const params = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/report-center-service/admin/compensation-structure/import-data-lock?${params}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return error;
    })
    .finally();
}

// 数据导出
function exportData(data) {
  const params = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/report-center-service/admin/compensation-structure/export-data?${params}`,
      { responseType: "blob" }
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return error;
    })
    .finally();
}

// 推送合思
function pushHes(data) {
  const params = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/order-service/admin/hose/compensation-flow-create?${params}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return error;
    })
    .finally();
}

export default {
  importData,
  lockData,
  exportData,
  pushHes
};
