import { fetchPost, fetchGet } from "../fetch";
import qs from "qs";
// 折扣列表
function getDiscountList(data) {
  return fetchPost("/api/coupon-service/plan/usable", data, "");
}

function surveySend(data) {
  return fetchPost("/api/questionnaire-service/admin/survey/send", data, "");
}
// 意向客户转正式报名发送问卷调查
function surveySendMultiple(data) {
  return fetchPost(
    "/api/questionnaire-service/admin/survey/can-send-customer",
    data,
    ""
  );
}

function getSurveyUsable(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/questionnaire-service/admin/survey/usable?${new_data}`);
}
export default {
  async getDiscountList(data) {
    return getDiscountList(data);
  },
  surveySend,
  getSurveyUsable,
  surveySendMultiple
};
