import { fetchGet, fetchPost, fetchDel, fetchPatch, fetchImg } from "../fetch";
import qs from "qs";
const api_path = "/api/organization-service/diploma";

// 创建奖状模板
function createCitationModel(data) {
  return fetchPost(`${api_path}/create`, data, "");
}
// 删除奖状模板
function deleteCitationModel(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchDel(`${api_path}/delete?${new_data}`);
}
// 生成奖状
function generateCitation(data) {
  return fetchPost(`${api_path}/generate`, data, "");
}
// 奖状信息
function getCitationInfo(data) {
  return fetchGet(`${api_path}/info`, {
    params: data
  });
}
// 获取奖状模板列表
function getCitationModelList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`${api_path}/list?${new_data}`);
}
// 更新奖状模板
function updateCitationModel(data) {
  const { diploma_id } = data;
  // let new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchPatch(`${api_path}/update?diploma_id=${diploma_id}`, data);
}
// 查看奖状
function viewCitationList(data) {
  return fetchGet(`${api_path}/view`, {
    params: data
  });
}
function getImage(path) {
  return fetchImg(path);
}
export default {
  createCitationModel,
  deleteCitationModel,
  generateCitation,
  getCitationInfo,
  getCitationModelList,
  updateCitationModel,
  viewCitationList,
  getImage
};
