import { fetchGet, fetchDel, fetchPost, fetchPatch } from "../fetch";
import qs from "qs";
import axios from "../http";
import Vue from "vue";

function unbindWechat(data) {
  return fetchPost(
    `/api/questionnaire-service/admin/miniProgram/studentOpenId/unbind`,
    data,
    ""
  );
}
// 获取学员信息列表
function getStudentInforList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/student-service/student/list?${new_data}`);
}
// 获取待入班池学员信息列表
function getWaitClassList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/student-service/student/wait-school-list?${new_data}`);
}
// 获取学员信息列表
function getNewStudentInforList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/student-service/student/search-list?${new_data}`);
}
// 获取学员详情
function getStudentInforDetail(data) {
  return fetchGet("/api/student-service/student/info", { params: data });
}
// 获取学费列表
function getWalletList(data) {
  return fetchGet("/api/order-service/admin/wallet/good-list", {
    params: data
  });
}
// 修改学员信息
function saveStudentInforDetail(data) {
  return fetchPatch(`/api/student-service/student/update`, data);
}
// 修改学员标签信息
function saveStudentLabel(data) {
  return fetchPatch(`/api/student-service/student/labelUpdate`, data);
}
// 修改学员信息管理标签信息
function saveCustomerLabel(data) {
  return fetchPatch(
    `/api/market-service/customer/updateLabel?customer_id=${data.id}`,
    data
  );
}
// 删除
function delStudent(data) {
  return fetchDel("/api/student-service/student/delete", { data });
}
// 学员类别字典
function getStudentType(data) {
  return fetchGet("/api/student-service/student-category/list", {
    params: data
  });
}
// 学员状态字典
function getStudentState(data) {
  return fetchGet("/api/student-service/student/getStudentStateList", {
    params: data
  });
}
// 学员状态字典
function getStudentStyleList(data) {
  return fetchGet("/api/student-service/student/getStudentStyleList", {
    params: data
  });
}
// 导出
function exportStudentInfo(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/report-center-service/admin/student/export?${new_data}`,
    {
      params: { exportData: 1 }
    }
  );
}
// 临时导出
function exportStudentTemp(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/student-service/student/export?${new_data}`, {
    params: {}
  });
}
// 导出轮询成功后下载
function exportDownFile(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/student-service/student/download?${new_data}`, {
    params: { exportData: 1 }
  });
}
// 导出轮询
function exportLoop(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/student-service/student/export-rolling?${new_data}`, {
    params: {}
  });
}

// 校验 退学、休学日期是否合法
function checkSchoolDate(data) {
  return fetchGet("/api/school-service/scheduling/student/check-date-isvalid", {
    params: data
  });
}

// 休学
function outSchoold(data) {
  return axios
    .post(`/api/student-service/student/outSchool`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 退学
function leaveSchool(data) {
  return axios
    .post(`/api/student-service/student/dropSchool`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 导入学员
function importStudent(data) {
  return axios
    .post(`/api/student-service/student/import`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return error;
    })
    .finally();
}

// 下载模版
function downLoadTemplate(data) {
  return axios
    .get("/api/student-service/student/demoDownload", {
      params: { ...data, exportData: 1 }
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 恢复入学
function restoreSchoolState(data) {
  return axios
    .patch(`/api/student-service/student/recoverToSchool`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 复学
function regainSchool(data) {
  return axios
    .patch(`/api/student-service/student/backToSchool`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 原因列表
function causeList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/student-service/student/leaveSchoolReasonList?${new_data}`
  );
}

// 学费列表
function getTuitionData(data) {
  return axios
    .get("/api/enterprise/wallet/course/pageAggregate", { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 撤销退学、休学
function undoSchoolHandle(data) {
  return axios
    .patch(`/api/student-service/student/revokeStatus`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 课消明细
function cancelClassHandle(data) {
  return axios
    .get(`/api/order-service/admin/deduct/deduct-list`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 优惠券可领学生列表
function getStudentCouponList(data) {
  return axios
    .get(`/api/student-service/coupon/student-list`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 优惠券可领意向学生列表
function getCustomerCouponList(data) {
  return axios
    .get(`/api/market-service/coupon/customer-list`, { params: data })
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 批量修改
function manyUpdateEducation(data) {
  return axios
    .patch(`/api/student-service/student/manyUpdateEducation`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 学员优惠券明细列表
function getStudentUsedCouponList(data) {
  return axios
    .get(`/api/coupon-service/coupon-detail/student-used-list`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 加微状态更新
function updateWechatStatus(data) {
  return axios
    .post(`/api/student-service/student/update-wechat-status`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取学员操作记录
function getOperationCycleList(data) {
  // const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/student-service/operation-cycle/list`, {
    params: data
  });
}
// 获取学员操作记录详细信息
function getOperationCycleDetail(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/student-service/operation-cycle/detail?${new_data}`);
}

// 退费原因列表
function getNovalidReasonList(data) {
  // const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/market-service/customer/no-valid-reason`, {
    params: data
  });
}

// 获取学员下租赁物品
function getAllocationList(data) {
  // const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/order-service/admin/wallet/allocation-list`, {
    params: data
  });
}

// 获取学员下物品
function getArticleList(data) {
  // const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/order-service/admin/wallet/article-list`, {
    params: data
  });
}

export default {
  async unbindWechat(data) {
    return unbindWechat(data);
  },
  async getStudentInforList(data) {
    return getStudentInforList(data);
  },
  async getWaitClassList(data) {
    return getWaitClassList(data);
  },
  async getStudentInforDetail(data) {
    return getStudentInforDetail(data);
  },
  async saveStudentInforDetail(data) {
    return saveStudentInforDetail(data);
  },
  async saveStudentLabel(data) {
    return saveStudentLabel(data);
  },
  async saveCustomerLabel(data) {
    return saveCustomerLabel(data);
  },
  async delStudent(data) {
    return delStudent(data);
  },
  async getStudentType(data) {
    return getStudentType(data);
  },
  async getStudentState(data) {
    return getStudentState(data);
  },
  async getStudentStyleList(data) {
    return getStudentStyleList(data);
  },
  async exportStudentInfo(data) {
    return exportStudentInfo(data);
  },
  // 临时学员导出
  async exportStudentTemp(data) {
    return exportStudentTemp(data);
  },
  async exportDownFile(data) {
    return exportDownFile(data);
  },

  async exportLoop(data) {
    return exportLoop(data);
  },

  async checkSchoolDate(data) {
    return checkSchoolDate(data);
  },
  async outSchoold(data) {
    return outSchoold(data);
  },
  async leaveSchool(data) {
    return leaveSchool(data);
  },
  async restoreSchoolState(data) {
    return restoreSchoolState(data);
  },
  async causeList(data) {
    return causeList(data);
  },
  async regainSchool(data) {
    return regainSchool(data);
  },
  async undoSchoolHandle(data) {
    return undoSchoolHandle(data);
  },
  async cancelClassHandle(data) {
    return cancelClassHandle(data);
  },
  async getStudentCouponList(data) {
    return getStudentCouponList(data);
  },
  async getCustomerCouponList(data) {
    return getCustomerCouponList(data);
  },
  async getTuitionData(data) {
    return getTuitionData(data);
  },
  async manyUpdateEducation(data) {
    return manyUpdateEducation(data);
  },
  async importStudent(data) {
    return importStudent(data);
  },
  async downLoadTemplate(data) {
    return downLoadTemplate(data);
  },
  async getStudentUsedCouponList(data) {
    return getStudentUsedCouponList(data);
  },
  async getWalletList(data) {
    return getWalletList(data);
  },
  async updateWechatStatus(data) {
    return updateWechatStatus(data);
  },
  async getNewStudentInforList(data) {
    return getNewStudentInforList(data);
  },
  getOperationCycleList,
  getOperationCycleDetail,
  getNovalidReasonList,
  getAllocationList,
  getArticleList
};
