import { fetchGet, fetchPost, fetchUploadFile } from "../fetch";
import qs from "qs";
// 授权校区
function school_auth(data) {
  return fetchPost("/api/organization-service/databank/auth", data);
}
// 获取分类列表
function getList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/organization-service/databank/list?${new_data}`);
}

// 获取分类详情
function getInfo(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/organization-service/databank/info?${new_data}`);
}

// 删除分类
function del(data) {
  return fetchPost("/api/organization-service/databank/delete", data);
}
// 上传
function upload(data) {
  return fetchPost("/api/organization-service/databank/upload", data);
}
// 创建分类
function create(data) {
  return fetchPost("/api/organization-service/databank/create", data);
}
// 获取分类文件列表
function getFileList(data) {
  // const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/organization-service/databank/file-list`, {
    params: data
  });
}
// 上传文件
function uploadFile(data) {
  return fetchUploadFile(`/api/school-service/image/upload`, data);
}

function feedbackSend(data) {
  return fetchPost("/api/school-service/admin/feedback/send", data);
}

export default {
  async school_auth(data) {
    return school_auth(data);
  },
  async getList(data) {
    return getList(data);
  },
  async getInfo(data) {
    return getInfo(data);
  },
  async del(data) {
    return del(data);
  },
  async create(data) {
    return create(data);
  },
  async getFileList(data) {
    return getFileList(data);
  },
  async upload(data) {
    return upload(data);
  },
  // 富文本上传
  async uploadFile(data) {
    return uploadFile(data);
  },
  async feedbackSend(data) {
    return feedbackSend(data);
  }
};
