import qs from "qs";
const fetch = require("../fetch");

// 学员的电子钱包
function getStudentWalletBalance(data) {
  return fetch.fetchGet(`/api/order-service/admin/wallet/info`, {
    params: data
  });
}
// 学员电子钱包统计
function getWalletStatistics(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/wallet/statistics?${new_data}`
  );
}
// 学员电子钱包合计
function getWalletStatisticsTotal(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/wallet/statistics/total?${new_data}`
  );
}
// 电子钱包进出明细
function getWalletEntryExitDetail(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/wallet/entry/exit/detail?${new_data}`
  );
}
// 电子钱包进出明细合计
function getWalletEntryExitDetailTotal(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/wallet/entry/exit/detail/total?${new_data}`
  );
}
// 电子钱包统计明细
function getWalletDetail(data) {
  return fetch.fetchGet(`/api/order-service/admin/wallet/detail`, {
    params: data
  });
}
// 学员费用预警查询
function getWalletWarnList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/wallet/warn/list?${new_data}`
  );
}
// 学员费用预警查询合计
function getWalletWarnListTotal(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/wallet/warn/list/total?${new_data}`
  );
}
// 电子钱包费用明细导出
function exportDetailExcel(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/wallet/detail/export?${new_data}`,
    {
      params: { exportData: 1 }
    }
  );
}
// 学员电子钱包统计导出
function exportStatisticsExcel(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/wallet/statistics/export?${new_data}`,
    {
      params: { exportData: 1 }
    }
  );
}
// 电子钱包进出明细导出
function exportEntryExitDetailExcel(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/wallet/entry/exit/detail/export?${new_data}`,
    {
      params: { exportData: 1 }
    }
  );
}

// function exportWarnDetailExcel(data) {
//   const new_data = qs.stringify(data, { arrayFormat: "repeat" });
//   return fetch.fetchGet(
//     `/api/order-service/admin/wallet/warn/list/export?${new_data}`,
//     {
//       params: { exportData: 1 }
//     }
//   );
// }

// 学员费用预警查询导出
function exportWarnDetailExcel(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/wallet/warn/list/export?${new_data}`
  );
}

// 学员费用预警查询导出
function exportLoopWarn(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/wallet/warn/export-rolling?${new_data}`
  );
}

// 学员费用预警查询导出
function exportDownFileWarn(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/wallet/warn/download?${new_data}`,
    {
      params: { exportData: 1 }
    }
  );
}

function getExportExcelFileId(url, data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(`${url}?${new_data}`);
}

function exportLoop(url, data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(`${url}?${new_data}`);
}

function exportDownFile(url, data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(`${url}?${new_data}`, {
    params: { exportData: 1 }
  });
}
// 新版同步导出
function exportExcelSyncNew(url, data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(`${url}?${new_data}`, {
    params: { exportData: 1 }
  });
}
export default {
  getStudentWalletBalance,
  getWalletStatistics,
  getWalletDetail,
  getWalletEntryExitDetail,
  exportDetailExcel,
  exportStatisticsExcel,
  exportEntryExitDetailExcel,
  getWalletWarnList,
  exportWarnDetailExcel,
  exportLoopWarn,
  exportDownFileWarn,
  getExportExcelFileId,
  exportLoop,
  exportDownFile,
  getWalletEntryExitDetailTotal,
  getWalletWarnListTotal,
  getWalletStatisticsTotal,
  exportExcelSyncNew
};
