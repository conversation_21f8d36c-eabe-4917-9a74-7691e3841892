import axios from "../http";
import Vue from "vue";
import qs from "qs";

// 模板列表
function surveyTemplateAllList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/questionnaire-service/admin/surveyTemplate/allList?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 创建模板
function surveyTemplateCreate(data) {
  return axios
    .post(`/api/questionnaire-service/admin/surveyTemplate/create`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 删除模板
function surveyTemplateDelete(data) {
  return axios
    .post(`/api/questionnaire-service/admin/surveyTemplate/delete`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 模板详情
function surveyTemplateDetail(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/questionnaire-service/admin/surveyTemplate/detail?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 模板列表
function surveyTemplateList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/questionnaire-service/admin/surveyTemplate/list?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 模板列表
function surveyTemplateMetaInfo(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/questionnaire-service/admin/surveyTemplate/metaInfo?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 模板更新
function surveyTemplateUpdate(data) {
  return axios
    .post(`/api/questionnaire-service/admin/surveyTemplate/update`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 模板更新配置
function surveyTemplateUpdateConf(data) {
  return axios
    .post(`/api/questionnaire-service/admin/surveyTemplate/updateConf`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 模板更新状态
function surveyTemplateUpdateEnabledStatus(data) {
  return axios
    .post(
      `/api/questionnaire-service/admin/surveyTemplate/updateEnabledStatus`,
      data
    )
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
export default {
  async surveyTemplateAllList(data) {
    return surveyTemplateAllList(data);
  },
  async surveyTemplateCreate(data) {
    return surveyTemplateCreate(data);
  },
  async surveyTemplateDelete(data) {
    return surveyTemplateDelete(data);
  },
  async surveyTemplateDetail(data) {
    return surveyTemplateDetail(data);
  },
  async surveyTemplateList(data) {
    return surveyTemplateList(data);
  },
  async surveyTemplateUpdate(data) {
    return surveyTemplateUpdate(data);
  },
  async surveyTemplateUpdateConf(data) {
    return surveyTemplateUpdateConf(data);
  },
  async surveyTemplateUpdateEnabledStatus(data) {
    return surveyTemplateUpdateEnabledStatus(data);
  },
  async surveyTemplateMetaInfo(data) {
    return surveyTemplateMetaInfo(data);
  }
};
