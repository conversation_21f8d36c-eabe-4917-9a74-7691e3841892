<?xml version="1.0" encoding="UTF-8"?>
<svg width="35px" height="38px" viewBox="0 0 35 38" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>摄像头</title>
    <g id="主播端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="底端视频预览备份-32" transform="translate(-481.000000, -1032.000000)">
            <g id="摄像头" transform="translate(482.000000, 1033.000000)">
                <g transform="translate(3.600000, 1.800000)" fill="#FFFFFF">
                    <path d="M14.58,0 C22.6323117,0 29.16,6.52768835 29.16,14.58 C29.16,22.6323117 22.6323117,29.16 14.58,29.16 C6.52768835,29.16 0,22.6323117 0,14.58 C0,6.52768835 6.52768835,0 14.58,0 Z M14.58,6.48 C10.1064935,6.48 6.48,10.1064935 6.48,14.58 C6.48,19.0535065 10.1064935,22.68 14.58,22.68 C19.0535065,22.68 22.68,19.0535065 22.68,14.58 C22.68,10.1064935 19.0535065,6.48 14.58,6.48 Z" id="形状结合"></path>
                    <circle id="椭圆形" cx="14.58" cy="14.58" r="4.86"></circle>
                    <ellipse id="椭圆形" cx="14.58" cy="30.132" rx="11.664" ry="1.944"></ellipse>
                </g>
                <path d="M30.7547557,-0.49739297 C31.2778461,-0.523926542 31.8111814,-0.348542892 32.2303587,0.0362091998 C32.6124291,0.386901875 32.8331975,0.851126689 32.8870245,1.3327624 C32.9426068,1.83010295 32.8197248,2.3454978 32.5145885,2.77273878 L32.5145885,2.77273878 L3.06010887,35.8176919 C2.68109968,36.242668 2.16796351,36.4708782 1.6452443,36.497393 C1.12215391,36.5239265 0.588818597,36.3485429 0.169641345,35.9637908 C-0.212429071,35.6130981 -0.433197451,35.1488733 -0.487024546,34.6672376 C-0.542606796,34.1698971 -0.419724841,33.6545022 -0.114588504,33.2272612 L-0.114588504,33.2272612 L29.3398911,0.182308055 C29.7189003,-0.24266795 30.2320365,-0.470878225 30.7547557,-0.49739297 Z" id="直线" stroke="#383C4A" fill="#F06130" fill-rule="nonzero"></path>
            </g>
        </g>
    </g>
</svg>