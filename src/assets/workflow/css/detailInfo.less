.detail {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  border-bottom: 1px solid #e0e6ed;
  &:last-child {
    border-bottom: none;
  }
  .item {
    width: 50%;
    height: 20px;
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 16px;
    .item__label {
      font-size: @text-size_small;
      font-family: @text-famliy_medium;
      color: @text-color_third;
      width: 70px;
      display: inline-block;
    }
    .item__content {
      font-size: @text-size_normal;
      font-family: @text-famliy_medium;
      color: @text-color_first;
      width: calc(100% - 96px);
      display: inline-block;
      margin-left: 16px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}