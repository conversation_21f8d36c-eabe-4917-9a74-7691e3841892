.tg-header__title {
  font-size: 24px;
  font-family: "PingFangSC-Semibold, sans-serif,Arial";
  height: 68px;
  line-height: 68px;
  color: #1f2d3d;
  padding-left: 6px;
  padding-right: 6px;
}
button.tg-button--primary {
  height: 32px;
  font-family: "PingFangSC-Light, sans-serif,Arial";
  border-radius: 4px;
  padding: 0 22px;
  font-weight: normal;
}
button.tg-button--plain {
  background-color: transparent;
  border: 1px solid #2d80ed;
  color: #2d80ed;
  padding: 0 22px;
  height: 32px;
  font-family: "PingFangSC-Light, sans-serif,Arial";
  font-weight: normal;
}
button.tg-button--plain:hover {
  background-color: #2d80ed;
  color: #fff;
}
button.tg-button--mini {
  width: 64px;
  padding: 0;
}
button.tg-button--disabled {
  color: #c0ccda;
  border-color: #c0ccda;
}
button.tg-button--disabled:hover {
  background-color: transparent;
  color: #c0ccda;
}
.container {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  margin: 0;
  max-width: 100% !important;
}
/**表格**/
::v-deep .el-table {
  padding: 0 16px;
  height: calc(100% - 52px);
}
::v-deep.tg-table__box:not(:has(.tg-pagination)) .el-table {
  height: 100%;
}
.table-container:not(:has(.tg-pagination))::after {
  bottom: 0px;
}
::v-deep .el-table__header {
  border-radius: 4px;
  box-shadow: 0 2px 0 0 #ebf4ff;
}
::v-deep .el-table__header th {
  font-weight: 600;
}
::v-deep .el-table__body-wrapper {
  height: calc(100% - 46px);
  z-index: 2;
}
::v-deep .el-table .cell {
  font-family: "PingFangSC-Medium, sans-serif,Arial";
  font-size: 14px;
}
::v-deep th.el-table-column--selection .cell {
  padding-left: 14px;
  padding-right: 14px;
}
::v-deep .el-table td,
::v-deep .el-table th {
  padding: 0 !important;
  height: 46px;
  user-select: auto;
}
/**复选框**/
::v-deep .el-checkbox__inner {
  border: 1px solid #2d80ed;
  border-radius: 1px;
  height: 14px;
  width: 14px;
}
div.tg-pagination {
  background-color: #fff;
  height: 52px;
  line-height: 52px;
  padding: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 17px;
  padding-right: 24px;
  justify-content: space-between;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}
/**分页**/
::v-deep .el-pagination {
  background-color: transparent;
  height: 30px;
}
::v-deep .el-pagination .el-select .el-input {
  margin: 0;
}
::v-deep .el-pagination .el-input.is-focus::after {
  height: 28px;
  border-radius: 5px;
}
::v-deep .el-pagination__total {
  color: #8492a6;
  font-size: 13px;
}
::v-deep .el-pagination__editor.is-in-pagination {
  line-height: 28px;
}
::v-deep .el-pagination.is-background .btn-next,
::v-deep .el-pagination.is-background .btn-prev,
::v-deep .el-pagination.is-background .el-pager li {
  min-width: 28px !important;
  height: 28px;
  line-height: 28px;
  border-radius: 4px;
  padding: 0;
  margin: 0 4px;
  color: #475669;
  border: 1px solid #e1e7ef;
  font-weight: normal;
  background-color: transparent;
}
::v-deep .el-pagination__jump {
  margin-left: 4px;
}
::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
  color: #2d80ed;
  background-color: transparent;
  border-color: #2d80ed;
}
::v-deep .el-pagination__editor.el-input {
  width: 36px;
}
::v-deep .el-pagination.is-background .el-pager li {
  line-height: 26px;
}
::v-deep .el-pagination__sizes {
  display: inline-flex;
}
::v-deep .el-pagination__sizes .el-input__inner,
::v-deep .el-pagination__sizes .el-select .el-input .el-select__caret {
  height: 28px;
  line-height: 28px;
}
.tg-box--margin {
  margin-top: 16px;
}
.tg-box--width {
  width: 100%;
}
button.tg-text--blue,
span.tg-text--blue {
  white-space: normal;
  word-break: break-all;
  margin-right: 8px;
  color: #157df0;
  font-family: "PingFangSC-Medium, sans-serif,Arial";
  font-size: 13px;
  font-weight: normal;
}
button.tg-text--green,
span.tg-text--green {
  white-space: normal;
  word-break: break-all;
  margin-right: 8px;
  color: #2d80ed;
  font-family: "PingFangSC-Medium, sans-serif,Arial";
  font-size: 13px;
  font-weight: normal;
}
/**评分**/
::v-deep .el-rate {
  width: 124px;
  height: 16px;
  display: flex;
}
::v-deep .el-rate__icon.el-icon-star-off {
  color: #2d80ed !important;
  font-size: 16px;
}
::v-deep .el-rate__icon.el-icon-star-on {
  color: #2d80ed !important;
}
/**input框**/
::v-deep .el-input__inner {
  height: 32px;
  line-height: 32px;
}
::v-deep .el-input,
::v-deep .el-date-editor.el-input,
::v-deep .el-date-editor.el-input__inner {
  width: 168px;
}
/**表单form**/
::v-deep .el-form-item {
  margin-bottom: 0;
}
::v-deep .el-form--inline div.el-form-item {
  margin-right: 20px;
}
::v-deep .el-form-item__label {
  padding: 0 10px 0 0;
}
::v-deep .el-form-item__error {
  padding-top: 0;
  top: 90%;
  display: none;
}
.tg_margin_top_second {
  margin-top: 10px;
}
.tg-shadow--margin {
  margin-left: 6px;
  margin-right: 6px;
}
.tg-box--shadow {
  box-shadow: 0 2px 6px 0 rgba(204, 208, 217, 0.35);
}
.tg-table {
  width: 100%;
  border-radius: 4px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
.tg-table__box {
  box-shadow: 0 2px 6px 0 rgba(204, 208, 217, 0.35);
  margin: 16px 6px 6px 6px;
  border-radius: 4px;
  position: relative;
  width: 100%;
  flex: 1;
}
.tg-box--border {
  width: calc(100% - 2px);
  height: 44px;
  border: 1px solid #2d80ed;
  border-radius: 4px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  pointer-events: none;
  box-shadow: 0 2px 0 0 #ebf4ff;
}
button.tg-button__icon {
  padding-left: 12px;
  padding-right: 12px;
}
.tg-button__icon--normal {
  width: 10px;
  height: 11px;
  margin-right: 9px;
}
::v-deep .el-select .el-input .el-select__caret,
::v-deep .el-cascader {
  height: 32px;
  line-height: 32px;
}
.tg-button__icon--right {
  float: right;
}
::v-deep .el-dialog {
  border: 1px solid #2d80ed;
  border-radius: 4px;
}
::v-deep .el-dialog__header,
::v-deep .el-drawer__header {
  padding: 0;
  height: 55px;
  line-height: 55px;
  padding-left: 24px;
  position: relative;
  border-bottom: 1px solid #e9f0f7;
  font-weight: 600;
}
::v-deep .el-dialog__header::after,
::v-deep .el-drawer__header::after {
  content: "";
  position: absolute;
  background-color: #2d80ed;
  top: 17px;
  left: 16px;
  height: 20px;
  width: 2px;
  z-index: 1;
  border-radius: 2px;
}
::v-deep .el-dialog__header .el-dialog__title,
::v-deep .el-drawer__header .el-dialog__title,
::v-deep .el-dialog__header .dialog-title,
::v-deep .el-drawer__header .dialog-title {
  font-family: "PingFangSC-Semibold, sans-serif,Arial";
  font-size: 16px;
  font-weight: 600;
}
::v-deep .el-dialog__footer {
  padding: 11px 16px;
  border-top: 1px solid #e9f0f7;
}
::v-deep .el-dialog__footer button.tg-button--primary,
::v-deep .el-dialog__footer button.tg-button--plain {
  padding: 0 18px;
}
::v-deep .el-drawer__header {
  margin: 0;
}
/**复选框**/
::v-deep .el-checkbox__label {
  font-family: "PingFangSC-Medium, sans-serif,Arial";
  font-weight: normal;
}
::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
  color: #1f2d3d !important;
}
::v-deep .el-date-editor .el-range-separator,
::v-deep .el-date-editor .el-range__icon {
  height: 32px;
}
::v-deep .el-range-editor.el-input__inner {
  width: 270px;
}
::v-deep .el-date-editor .el-range-separator {
  width: 20px;
}
::v-deep .el-date-editor.is-active::after {
  content: "";
  position: absolute;
  top: -3px;
  left: -3px;
  border: 2px solid #ebf4ff;
  border-radius: 6px;
  width: calc(100% + 2px);
  height: 32px;
  z-index: 3000;
}
::v-deep .el-input__icon.el-icon-date::before {
  content: "";
  display: block;
  width: 12px;
  height: 12px;
  background: url("~@/assets/图片/icon_date.png");
  background-size: cover;
  position: absolute;
  top: 9px;
  left: 5px;
}
::v-deep .el-input__icon.el-range__icon.el-icon-date::before {
  left: 14px;
}
::v-deep .el-date-editor .el-range-input:first-of-type {
  margin-left: 12px;
}
::v-deep .el-date-editor .el-input__icon {
  line-height: 32px;
}
::v-deep .el-date-editor .el-range__close-icon {
  line-height: 25px;
  color: #98a9bf;
  text-align: right;
}
/**下拉框**/
.el-select-dropdown {
  border: solid 1px #2d80ed !important;
  margin-top: 0px !important;
  margin-bottom: 0px !important;
}
.el-select-dropdown .popper__arrow {
  display: none !important;
}
/***日期选择***/
.el-picker-panel {
  border: solid 1px #2d80ed !important;
}
::v-deep.el-popper[x-placement^="bottom"] {
  margin-top: 0;
}
::v-deep .el-select-dropdown {
  border: 1px solid #2d80ed !important;
  margin-top: 0px !important;
  border-top-left-radius: 0 !important;
  border-top-right-radius: 0 !important;
}
::v-deep .el-select .el-select-dropdown .popper__arrow {
  display: none;
}
::v-deep .el-select-dropdown__item.selected {
  background-color: #ebf4ff;
  font-weight: normal;
}
::v-deep .el-select-dropdown__item.hover {
  background-color: #ebf4ff;
  font-weight: normal;
}
::v-deep .el-select-dropdown__item {
  padding: 0;
  margin: 0 10px;
  border-radius: 4px;
  text-align: center;
}
::v-deep .el-select-dropdown.el-popper {
  width: inherit;
}
::v-deep .el-input.is-focus::after {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  border: 2px solid #ebf4ff;
  border-radius: 6px;
  width: 100%;
  height: 32px;
  z-index: 3000;
}
::v-deep .el-input__inner::placeholder,
::v-deep .el-textarea__inner::placeholder,
::v-deep .el-date-editor .el-range-input::placeholder {
  color: #8492a6;
}
::v-deep .el-input__inner::-webkit-input-placeholder,
::v-deep .el-textarea__inner::-webkit-input-placeholder,
::v-deep .el-date-editor .el-range-input::-webkit-input-placeholder {
  /* WebKit browsers 适配谷歌 */
  color: #8492a6;
}
::v-deep .el-input__inner:-moz-placeholder,
::v-deep .el-textarea__inner:-moz-placeholder,
::v-deep .el-date-editor .el-range-input:-moz-placeholder {
  /* Mozilla Firefox 4 to 18 适配火狐 */
  color: #8492a6;
  opacity: 1;
}
::v-deep .el-input__inner::-moz-placeholder,
::v-deep .el-textarea__inner::-moz-placeholder,
::v-deep .el-date-editor .el-range-input::-moz-placeholder {
  /* Mozilla Firefox 19+ 适配火狐 */
  color: #8492a6;
  opacity: 1;
}
::v-deep .el-input__inner:-ms-input-placeholder,
::v-deep .el-textarea__inner:-ms-input-placeholder,
::v-deep .el-date-editor .el-range-input:-ms-input-placeholder {
  /* Internet Explorer 10+  适配ie*/
  color: #8492a6;
}
::v-deep .el-input.is-disabled .el-input__inner {
  border-color: transparent;
}
::v-deep .el-input.is-disabled:hover .el-input__inner,
::v-deep .el-select .el-input.is-disabled:hover .el-input__inner {
  border-color: #2d80ed;
}
::v-deep .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap > .el-form-item__label:before,
::v-deep .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:before {
  color: #ff0317;
}
::v-deep .el-radio__label {
  font-family: "PingFangSC-Medium, sans-serif,Arial";
  font-size: 14px;
  color: #475669;
  font-weight: normal;
}
::v-deep .el-radio__label {
  padding-left: 12px;
}
::v-deep .el-radio__inner::after {
  width: 6px;
  height: 6px;
}
::v-deep .el-radio__input.is-checked .el-radio__inner::after {
  background-color: #2d80ed;
}
::v-deep .el-radio__input.is-checked .el-radio__inner {
  border-color: #bac8d9;
  background-color: transparent;
}
::v-deep .el-table__fixed {
  padding-right: 16px;
}
::v-deep .el-table__fixed .el-table__fixed-header-wrapper,
::v-deep .el-table__fixed .el-table__fixed-body-wrapper {
  left: 16px;
}
::v-deep .el-table__fixed-right {
  padding-left: 16px;
  box-sizing: border-box;
  background-color: #fff;
}
::v-deep .el-table__fixed-right .el-table__fixed-header-wrapper,
::v-deep .el-table__fixed-right .el-table__fixed-body-wrapper {
  right: 16px;
}
::v-deep .el-table__fixed-right th:last-child .cell,
::v-deep .el-table__fixed-right td:last-child .cell {
  padding-left: 26px;
}
::v-deep .el-input__icon {
  height: 32px;
  line-height: 32px;
}
::v-deep .el-date-editor.el-date-editor--date:focus-within::after,
::v-deep .el-date-editor.el-date-editor--time:focus-within::after {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  border: 2px solid #ebf4ff;
  border-radius: 6px;
  width: 100%;
  height: 32px;
  z-index: 3000;
}
button.tg-text--red {
  color: #ff0317;
}
button.tg-text--red:hover {
  color: #ff0317;
  opacity: 0.8;
}
/**树箭头*/
::v-deep .el-tree-node__expand-icon,
::v-deep .el-table__expand-icon {
  -webkit-transform: rotate(0deg);
  transform: rotate(0deg);
}
::v-deep .el-tree-node__expand-icon::before,
::v-deep .el-table__expand-icon i::before {
  content: "1";
  visibility: hidden;
}
::v-deep .el-tree-node__expand-icon {
  background: url("~@/assets/图片/icon_plus.png");
  background-size: cover;
  width: 16px;
  height: 16px;
  margin-right: 16px;
  margin-left: 13px;
  padding: 0;
}
::v-deep .el-table__expand-icon i {
  background: url("~@/assets/图片/icon_plus.png");
  background-size: cover;
  width: 14px;
  height: 14px;
  vertical-align: text-top;
  margin-top: 1px;
}
::v-deep .el-table__expand-icon {
  width: 14px;
  height: 14px !important;
  line-height: 14px !important;
}
::v-deep .el-tree-node.is-expanded .el-tree-node__expand-icon.el-icon-caret-right:before,
::v-deep .el-table__expand-icon--expanded i:before {
  content: "1";
  visibility: hidden;
}
::v-deep .el-tree-node.is-expanded .expanded.el-tree-node__expand-icon.el-icon-caret-right,
::v-deep .el-table__expand-icon--expanded i {
  background: url("~@/assets/图片/icon_minus.png");
  background-size: cover;
}
::v-deep .el-tree-node__expand-icon.is-leaf {
  visibility: hidden;
}
::v-deep .el-tree-node:focus > .el-tree-node__content {
  background-color: transparent !important;
}
.tg-text--black {
  color: #475669;
}
/**tab**/
::v-deep .el-tabs__nav-wrap::after {
  background-color: transparent;
}
::v-deep .el-tabs__item:focus.is-active.is-focus:not(:active) {
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}
/**el-dialog*/
::v-deep .el-dialog .el-icon-close:before,
::v-deep .el-drawer__wrapper .el-icon-close:before {
  content: "1";
  visibility: hidden;
}
::v-deep .el-dialog .el-icon-close,
::v-deep .el-drawer__wrapper .el-icon-close {
  background-image: url("~@/assets/图片/icon_close.png");
  width: 12px;
  height: 12px;
  background-size: cover;
}
::v-deep .el-dialog .el-icon-close:hover,
::v-deep .el-drawer__wrapper .el-icon-close:hover {
  background-image: url("~@/assets/图片/icon_close_ac.png");
}
.el-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-height: calc(100% - 80px);
  max-width: calc(100% - 30px);
  border: 1px solid #2d80ed;
  border-radius: 4px;
}
.el-dialog .el-select-dropdown {
  position: absolute !important;
  left: 0 !important;
  top: 32px !important;
}
.el-dialog .el-dialog__header,
.el-dialog .el-drawer__header {
  padding: 0;
  height: 55px;
  line-height: 55px;
  padding-left: 24px;
  position: relative;
  border-bottom: 1px solid #e9f0f7;
  font-weight: 600;
}
.el-dialog .el-dialog__header::after,
.el-dialog .el-drawer__header::after {
  content: "";
  position: absolute;
  background-color: #2d80ed;
  top: 17px;
  left: 16px;
  height: 20px;
  width: 2px;
  z-index: 1;
  border-radius: 2px;
}
.el-dialog .el-dialog__header .el-dialog__title,
.el-dialog .el-drawer__header .el-dialog__title,
.el-dialog .el-dialog__header .dialog-title,
.el-dialog .el-drawer__header .dialog-title {
  font-family: "PingFangSC-Semibold, sans-serif,Arial";
  font-size: 16px;
  font-weight: 600;
}
.el-dialog ::v-deep .el-dialog__footer {
  padding: 11px 16px;
  border-top: 1px solid #e9f0f7;
}
.el-dialog ::v-deep .el-dialog__footer button.tg-button--primary,
.el-dialog ::v-deep .el-dialog__footer button.tg-button--plain {
  padding: 0 18px;
}
::v-deep .el-dialog__body {
  overflow: auto;
}
/**操作...**/
::v-deep .tg-table--operate {
  cursor: pointer;
}
::v-deep .tg-table--operate:before {
  content: "";
  width: 16px;
  height: 4px;
  margin-left: 4px;
  vertical-align: middle;
  display: inline-block;
  background-image: url("~@/assets/图片/icon_more.png");
  background-size: cover;
}
::v-deep .tg-table--operate:hover:before {
  content: "";
  background-image: url("~@/assets/图片/icon_more_ac.png");
}
/**el-select多个*/
::v-deep .el-select .el-select__tags {
  overflow: scroll;
  box-sizing: border-box;
  max-width: unset;
  flex-wrap: unset;
}
::v-deep .el-select .el-tag.el-tag--info {
  background-color: #ebf4ff;
  border-radius: 4px;
  border-color: #2d80ed;
  height: 22px;
  line-height: 20px;
  display: inline-block;
  color: #475669;
  font-family: "PingFangSC-Medium, sans-serif,Arial";
  font-size: 14px;
  padding-right: 30px;
  padding-left: 0;
  margin-top: 0;
  margin-bottom: 0;
}
::v-deep .el-select .el-tag.el-tag--info .el-select__tags-text {
  padding-left: 16px;
}
::v-deep .el-select .el-tag.el-tag--info .el-tag__close.el-icon-close {
  background-image: url("~@/assets/图片/icon_close_green.png");
  width: 18px;
  height: 18px;
  color: transparent;
  right: -16px;
  display: inline-block;
  background-size: cover;
}
.tg-header__sub-title {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-left: 6px;
  margin-right: 6px;
  height: 46px;
  line-height: 46px;
  border: 1px solid #2d80ed;
  border-radius: 4px;
  padding-left: 16px;
  background-color: #fff;
}
.tg-header__sub-title img {
  width: 12px;
  height: 12px;
  margin-right: 10px;
}
.tg-header__sub-title span {
  color: #475669;
  font-family: "PingFangSC-Medium, sans-serif,Arial";
  font-size: 14px;
}
button.tg-span__divide-line {
  cursor: pointer;
  position: relative;
}
button.tg-span__divide-line:after {
  content: "";
  background: #cbcfda;
  width: 1px;
  height: 13px;
  position: absolute;
  top: 9px;
  margin-left: 9px;
}
button:last-child.tg-span__divide-line::after {
  background: transparent;
}
button.tg-table__name--ellipsis {
  width: 102%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: left;
}
.tg_ellipsis {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.tg-header__subtitle {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 0 6px 0 6px;
  height: 46px;
  line-height: 46px;
  border: 1px solid #2d80ed;
  border-radius: 4px;
  padding-left: 16px;
  background-color: #fff;
}
.tg-header__subtitle img {
  width: 12px;
  height: 12px;
  margin-right: 10px;
}
.tg-header__subtitle span {
  color: #475669;
  font-family: "PingFangSC-Medium, sans-serif,Arial";
  font-size: 14px;
}
.tg-tab ::v-deep .el-tabs__header {
  margin: 0;
  background: #fff;
  padding: 0 16px;
  box-shadow: 0 2px 6px 0 rgba(204, 208, 217, 0.35);
  border-radius: 4px;
}
.tg-tab ::v-deep .el-tabs__item {
  height: 46px;
  line-height: 46px;
}
.tg-tab ::v-deep .el-tab-pane {
  margin: 0 6px;
}
.tg-tab ::v-deep .el-tab-pane:first-child {
  margin: 0;
}
.tg-table--dark ::v-deep .el-table .el-table__header th {
  background: #f5f8fc;
}
.tg-table--dark .tg-box--border::after,
.tg-table--dark .tg-box--border::before {
  content: "";
  width: 16px;
  height: 44px;
  background: #f5f8fc;
  position: absolute;
  top: 0;
}
.tg-table--dark .tg-box--border::before {
  left: 0;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}
.tg-table--dark .tg-box--border::after {
  right: 0;
  border-bottom-right-radius: 4px;
  border-top-right-radius: 4px;
}
/*隐藏bpmn.js水印**/
.bjs-powered-by {
  display: none !important;
}
.tg-select--dialog ::v-deep .el-input__suffix {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.tg-select--dialog ::v-deep .el-input__suffix .el-input__suffix-inner {
  display: flex;
}
.tg-select--dialog .btn__img--dotted {
  width: 16px;
  height: 4px;
  cursor: pointer;
}
.tg-select--dialog ::v-deep .el-input__suffix .btn__img--dotted {
  margin-right: 10px;
}
.tg-select {
  cursor: pointer;
}
.tg-select:hover {
  position: relative;
}
.tg-select:hover::after {
  content: "";
  position: absolute;
  width: 100%;
  height: 32px;
  left: -2px;
  top: -2px;
  border: none;
  border-radius: 6px;
  z-index: 10;
}
.tg-select:hover ::v-deep .el-input__inner {
  border-color: #2d80ed;
}
::v-deep .el-select-dropdown__empty {
  color: #8492a6;
}
/**单选对号**/
.radio--tick ::v-deep .el-radio__inner {
  width: 16px;
  height: 16px;
}
::v-deep .radio--tick.is-checked .el-radio__input .el-radio__inner {
  background-color: #2d80ed;
  border: none;
}
::v-deep .radio--tick.is-checked .el-radio__inner::after {
  content: "";
  background: none;
  width: 7px;
  height: 3px;
  border: 2px solid white;
  border-top: transparent;
  border-right: transparent;
  text-align: center;
  display: block;
  position: absolute;
  top: 4px;
  left: 4px;
  transform: rotate(-45deg);
  border-radius: 0px;
}
::v-deep .radio--tick .el-radio__input.is-disabled .el-radio__inner {
  border: none;
}
::v-deep .el-table__fixed-footer-wrapper {
  left: 16px;
}
.el-breadcrumb__item:last-child .el-breadcrumb__inner:hover {
  color: #2d80ed;
  font-weight: 600;
}
.message-success .el-notification__title {
  color: #5cb87a;
}
.operationLog .popper__arrow::after {
  top: 0px!important;
  border-bottom-color: #2d80ed !important;
}
/**第三方插件vue-photo-preview图片预览样式重置*/
.pswp {
  z-index: 6666 !important;
}
