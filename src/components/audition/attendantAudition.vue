<!--跟班试听-->
<template>
  <div>
    <el-dialog
      :visible="true"
      title="跟班试听"
      class="attendant-audition"
      custom-class="attendant-audition"
      width="1016px"
      top="4vh"
      :before-close="cancel"
    >
      <el-form
        class="attendant-audition__content tg-box--margin"
        :model="form"
        :rules="rules"
        ref="form"
        :inline="true"
      >
        <el-form-item label="校区名称">
          <el-input
            v-model="form.department_name"
            placeholder="请选择所属校区"
            readonly
            show-word-limit
            :disabled="true"
            :validate-event="false"
            class="tg-select--dialog"
          >
            <img
              slot="suffix"
              src="../../assets/图片/icon_more.png"
              alt=""
              class="btn__img--dotted"
            />
          </el-input>
        </el-form-item>

        <el-form-item label="课程名称" class="custom--select">
          <el-input
            v-model="form.course_name"
            readonly
            placeholder="请选择课程"
            style="width: 190px"
            @click.native="choose_course_visible = true"
            @mouseenter.native="course_flag = true"
            @mouseleave.native="course_flag = false"
            :class="{ 'border--active': course_flag }"
          >
            <img
              :src="
                !course_flag
                  ? require('../../assets/图片/icon_more.png')
                  : require('../../assets/图片/icon_more_ac.png')
              "
              slot="suffix"
              alt=""
              class="more"
            />
          </el-input>
        </el-form-item>
        <el-form-item label="班级名称" class="custom--select">
          <el-input
            v-model="form.class_name"
            readonly
            style="width: 190px"
            placeholder="请选择班级"
            @click.native="choose_class_visible = true"
            @mouseenter.native="class_flag = true"
            @mouseleave.native="class_flag = false"
            :class="{ 'border--active': class_flag }"
          >
            <img
              :src="
                !class_flag
                  ? require('../../assets/图片/icon_more.png')
                  : require('../../assets/图片/icon_more_ac.png')
              "
              slot="suffix"
              alt=""
              class="more"
            />
          </el-input>
        </el-form-item>
        <el-button
          type="primary"
          class="tg-button--primary tg-button__icon"
          @click="handleSearch"
        >
          <img
            src="../../assets/图片/icon_search.png"
            alt=""
            class="tg-button__icon--normal"
          />查询</el-button
        >
        <el-button
          type="primary"
          class="tg-button--primary tg-button__icon"
          @click="remake"
        >
          <img
            src="../../assets/图片/icon_reset.png"
            alt=""
            class="tg-button__icon--normal"
          />重置</el-button
        >
        <el-row class="tg-box--margin">
          <el-form-item label="上课日期">
            <el-date-picker
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="record-label"
              v-model="form.timeChange"
              @change="timeChange"
              value-format="yyyy-MM-dd"
              popper-class="tg-date-picker tg-date--range"
              :pickerOptions="pickerOptions"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-checkbox v-model="has_is_started">包含已上课的排课</el-checkbox>
          </el-form-item>
          <el-form-item>
            <el-checkbox :disabled="true" v-model="has_is_full"
              >包含人数已满的排课</el-checkbox
            >
          </el-form-item>
        </el-row>
      </el-form>
      <el-row class="tg-box--margin">
        <el-form ref="show_form">
          <el-form-item label="上课学员列表" class="tg-form--large">
            <div class="student-list__wrap">
              <div
                v-for="(item, index) in studentList"
                :key="index"
                class="student"
              >
                {{ item.student_name }}
              </div>
            </div>
          </el-form-item>
        </el-form>
      </el-row>
      <div class="tg-table__box">
        <div class="tg-box--border"></div>
        <el-table
          ref="table"
          :data="list"
          tooltip-effect="dark"
          class="course-table tg-table"
          height="284"
          highlight-current-row
          @current-change="rowClick"
          :row-style="rowStyle"
          @row-click="rowClicks"
          v-loading="loading"
        >
          <el-table-column label="课程" width="150" prop="course_name">
          </el-table-column>
          <el-table-column label="班级" width="150" prop="classroom_name">
          </el-table-column>
          <el-table-column label="上课时间" width="160" prop="start_time">
            <template slot-scope="scope">
              {{ getTime(scope.row.start_time) }}
            </template>
          </el-table-column>
          <el-table-column label="星期" width="90" prop="week_day_chn">
          </el-table-column>
          <el-table-column label="时长" width="90" prop="class_duration">
            <template slot-scope="scope">
              {{
                moment.duration(scope.row.class_duration, "seconds").hours() +
                "时" +
                moment.duration(scope.row.class_duration, "seconds").minutes() +
                "分"
              }}
            </template>
          </el-table-column>
          <el-table-column label="任课老师" width="90" prop="teacher_name">
          </el-table-column>
          <el-table-column label="在班人数" width="90" prop="teacher_name">
            <template slot-scope="scope">
              {{ scope.row.total_student_numb }}/{{
                scope.row.pre_enrolment_numb
              }}
            </template>
          </el-table-column>
          <el-table-column label="教室" prop="school_room_name">
          </el-table-column>
        </el-table>
        <div class="tg-pagination">
          <span class="el-pagination__total">共 {{ total }} 条</span>
          <el-pagination
            background
            layout="prev, pager, next,jumper"
            :list="list"
            :total="total"
            :page-size="page_size"
            :current-page="page"
            @current-change="currentChange"
          >
          </el-pagination>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-checkbox v-model="check_conflict">检查上课冲突</el-checkbox>
        <div>
          <el-button class="tg-button--plain" type="plain" @click="cancel"
            >取消</el-button
          >
          <el-button
            class="tg-button--primary"
            type="primary"
            v-throttle="addStudentList"
            >确定</el-button
          >
        </div>
      </div>
    </el-dialog>
    <choose-course
      :check_id.sync="form.course_id"
      :check_name.sync="form.course_name"
      :check_arr.sync="course_check_arr"
      :choose_course_visible="choose_course_visible"
      v-if="choose_course_visible"
      @close="choose_course_visible = false"
      :status="true"
    ></choose-course>
    <choose-class
      :check_id.sync="form.classroom_id"
      :check_name.sync="form.class_name"
      :check_arr.sync="class_check_arr"
      :choose_class_visible="choose_class_visible"
      v-if="choose_class_visible"
      :department_id="[form.department_id]"
      @close="choose_class_visible = false"
    ></choose-class>
    <audition-record
      v-if="audtion_record_visible"
      @close="audtion_record_visible = false"
    ></audition-record>
  </div>
</template>
<script>
import timeFormat from "@/public/timeFormat";
import ChooseCourse from "@/components/studentInfo/chooseCourse.vue";
import ChooseClass from "@/components/studentInfo/chooseClass.vue";
import AuditionRecord from "@/components/audition/auditionRecord.vue";
import auditionApi from "@/api/audition";
import { picker_options } from "@/public/datePickerOptions";
export default {
  props: {
    studentList: Array,
    department_name: String,
    department_id: String
  },
  data() {
    return {
      type: "",
      list: [],
      form: {
        department_id: "",
        department_name: "",
        course_id: "",
        status: ["not_start"],
        course_name: "",
        classroom_id: "",
        class_name: "",
        is_full: false,
        class_time_begin: "",
        page_size: 10,
        class_time_over: ""
      },
      rules: {},
      school_tree_visible: false,
      choose_course_visible: false,
      choose_class_visible: false,
      course_flag: false,
      class_flag: false,
      page: 1,
      total: 0,
      page_size: 10,
      loading: false,
      current_row: {},
      audtion_record_visible: false,
      row: {},
      has_is_started: false,
      has_is_full: true,
      check_conflict: true,
      course_check_arr: [],
      class_check_arr: [],
      pickerOptions: picker_options
    };
  },
  watch: {
    School_Services_Cheduling_List(val) {
      this.loading = false;
      this.list = val.results;
      this.total = val.count;
    },
    schoolIds: {
      handler(val) {
        this.$set(this.form, "department_id", val);
      },
      immediate: true
    },
    schoolNames: {
      handler(val) {
        this.$set(this.form, "department_name", val.toString());
      },
      immediate: true
    },
    has_is_started: {
      handler(val) {
        const status = ["not_start", "is_started"];
        const init_status = ["not_start"];
        this.$set(this.form, "status", val ? status : init_status);
      },
      immediate: true
    },
    has_is_full: {
      handler(val) {
        this.$set(this.form, "is_full", val ? "" : false);
      },
      immediate: true
    }
  },
  computed: {
    // 班级列表
    School_Services_Cheduling_List() {
      return this.$store.getters.doneGetSchoolServicesChedulingAuditionList;
    },
    schoolIds() {
      return this.$store.getters.doneGetSchoolId;
    },
    schoolNames() {
      return this.$store.getters.doneGetSchoolName;
    }
  },
  mounted() {
    this.$set(this.form, "department_id", this.department_id);
    this.$set(this.form, "department_name", this.department_name);
    const search = this.changeSearch();
    this.loading = true;
    this.$store.dispatch("getSchoolServicesChedulingAuditionList", {
      page: 1,
      ...search
    });
  },
  methods: {
    rowClicks(row) {
      if (row.status === "is_started") {
        this.$refs.table.setCurrentRow();
      }
    },
    rowStyle({ row }) {
      if (row.status === "is_started") {
        return { color: "#C0CCDA" };
      }
    },
    timeChange(val) {
      this.$set(this.form, "class_time_begin", val == null ? "" : val[0]);
      this.$set(this.form, "class_time_over", val == null ? "" : val[1]);
    },
    remake() {
      this.form = {
        department_id: this.form.department_id,
        department_name: this.form.department_name,
        course_id: "",
        status: ["not_start"],
        course_name: "",
        classroom_id: "",
        class_name: "",
        is_full: false,
        class_time_begin: "",
        class_time_over: ""
      };
      this.has_is_started = false;
      this.has_is_full = false;
      const search = this.changeSearch();
      this.loading = true;
      this.$store.dispatch("getSchoolServicesChedulingAuditionList", {
        page: 1,
        ...search
      });
      this.class_check_arr = [];
      this.course_check_arr = [];
    },
    handleSearch() {
      const search = this.changeSearch();
      this.loading = true;
      this.$store.dispatch("getSchoolServicesChedulingAuditionList", {
        page: 1,
        ...search
      });
    },
    getTime(time) {
      return timeFormat.GetTime(time);
    },
    addStudentList() {
      if (!this.row.id) {
        this.$message.info("请选择排课");
        return;
      }
      const data = {
        scheduling_id: this.row.id,
        student_ids: this.studentList.map((item) => item.student_id),
        department_id: this.form.department_id,
        check_conflict: this.check_conflict
      };
      auditionApi.addStudent(data).then((res) => {
        if (typeof res.err === "undefined") {
          this.$message.success("添加成功");
          // this.getStudents({ scheduling_id: this.id });
          this.temp_remove_visible = true;
          this.$emit("really");
        } else {
          this.$message.error(res?.err);
        }
        // this.$store.dispatch("getSchoolServiceAuditionList", {
        //   page: 1,
        // });
      });
    },
    currentChange(val) {
      this.page = val;
      const search = this.changeSearch();
      this.loading = true;
      this.$store.dispatch("getSchoolServicesChedulingAuditionList", {
        page: val,
        ...search
      });
    },
    handleCurrentChange(val) {
      this.current_row = val;
      this.audtion_record_visible = true;
    },
    cancel() {
      this.$emit("close");
    },
    rowClick(row) {
      this.row = row;
    },
    changeSearch() {
      const search = JSON.parse(JSON.stringify(this.form));
      if (search.course_id !== "")
        search.course_id = search.course_id.split(",");
      return search;
    }
  },
  components: {
    ChooseCourse,
    ChooseClass,
    AuditionRecord
  },
  created() {}
};
</script>
<style lang="less" scoped>
.attendant-audition {
  ::v-deep & > .el-dialog__body {
    padding: 0 16px;
    height: 573px;
    box-sizing: border-box;
    .tg-table__box {
      margin-left: 0 !important;
      margin-top: 6px;
    }
  }
  ::v-deep .el-form-item__label,
  ::v-deep .el-form-item__content {
    line-height: 32px;
  }
  .custom--select {
    ::v-deep .el-input .el-input__inner {
      cursor: pointer;
    }
    ::v-deep .el-input .el-input__suffix-inner {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      height: inherit;
      margin-right: 10px;
      cursor: pointer;
    }
    img {
      height: 4px;
      width: 16px;
    }
  }
  ::v-deep .el-form--inline div.el-form-item.tg-form--large {
    margin-right: 0;
    display: flex;
  }
  .student-list__wrap {
    width: 888px;
    box-sizing: border-box;
    border: 1px solid #d0dce7;
    border-radius: 4px;
    display: inline-flex;
    flex-direction: row;
    flex-wrap: wrap;
    padding: 16px;
    height: 108px;
    overflow-y: auto;
    align-content: flex-start;
    .student {
      box-sizing: border-box;
      border: 1px solid @base-color;
      background: #ebf4ff;
      border-radius: 4px;
      height: 30px;
      line-height: 30px;
      font-size: @text-size_normal;
      font-family: @text-famliy_medium;
      color: @text-color_second;
      text-align: center;
      margin-bottom: 16px;
      margin-right: 16px;
    }
    .student:nth-child(8n) {
      margin-right: 0;
    }
  }
  .dialog-footer {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  ::v-deep .course-table {
    &:before,
    &:after {
      background: #f5f8fc;
      content: "";
      height: 48px;
      width: 16px;
      position: absolute;
      left: 0;
      top: 0;
    }
    &:before {
      right: 0;
      bottom: unset;
      left: unset;
    }
    th {
      background: #f5f8fc;
    }
    .el-table__body tr.current-row > td {
      background-color: #ebf4ff;
    }
  }
  .tg-pagination {
    border-top: 1px solid #e0e6ed;
  }
  ::v-deep .student-list__wrap .student {
    padding: 0 25px !important;
  }
  ::v-deep .el-table tr:last-child td {
    border-bottom: none;
  }
}
</style>
