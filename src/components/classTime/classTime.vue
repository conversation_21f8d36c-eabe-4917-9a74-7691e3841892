<template>
  <el-dialog
    :visible="true"
    :modal-append-to-body="false"
    :modal="true"
    width="1016px"
    :close-on-press-escape="false"
    class="time-setting-dialog"
    :show-close="true"
    :before-close="handleClose"
  >
    <div slot="title">
      <span>{{ title }}</span>
    </div>
    <div class="tg-dialog__content">
      <!-- <el-input placeholder="输入关键字进行过滤" v-model="filterText">
      </el-input> -->
      <el-form :inline="true" ref="school" @submit.native.prevent>
        <el-form-item label="">
          <el-input
            prefix-icon="el-icon-search"
            placeholder="请输入校区名称"
            v-model="filterText"
            style="width: 250px"
          />
        </el-form-item>
        <el-form-item label="">
          <el-button
            class="tg-button--primary"
            type="primary"
            @click="searchVal"
            >查询</el-button
          >
        </el-form-item>
        <el-form-item label="">
          <el-checkbox
            :indeterminate="isIndeterminate"
            v-model="all_checked"
            @change="handleCheckAllChange"
            >全选</el-checkbox
          >
        </el-form-item>
      </el-form>
      <div class="tg-tree--custom">
        <div v-if="school_data_temp.length">
          <template v-for="area in school_data_temp">
            <div :key="area.id" v-if="!area.isHide" class="leaf">
              <div @click="expanded(area, $event)" class="title">
                <span
                  :class="{ active: area.expanded }"
                  class="expanded"
                ></span>
                <span class="pos__icon"></span>
                <span class="name">{{ area.name }}</span>
                <span class="num"
                  >({{ area.child ? area.child.length : 0 }})</span
                >
              </div>

              <div
                class="content"
                :class="{ show: area.expanded }"
                v-if="area.child && area.child.length"
              >
                <template v-for="sch in area.child">
                  <el-checkbox
                    :title="sch.name"
                    :key="sch.id"
                    v-if="!sch.isHide"
                    :disabled="sch.disabled"
                    v-model="sch.checked"
                    @change="checkChange"
                    >{{ sch.name | sub_str }}</el-checkbox
                  ></template
                >
                <!-- <el-checkbox
                :title="sch.name"
                :key="sch.id"
                v-for="sch in area.child"
                :disabled="sch.disabled"
                v-model="sch.checked"
                @change="checkChange"
                >{{ sch.name | sub_str }}</el-checkbox
              > -->
              </div>
              <div v-else class="content show no-data">无数据</div>
            </div></template
          >
        </div>
        <div v-else class="no-data">无数据</div>
      </div>
      <!-- <el-tree
        class="tg-box--margin school-tree_select"
        :data="schoolList"
        :props="defaultProps"
        default-expand-all
        node-key="id"
        ref="tree"
        :show-checkbox="true"
      >
        <span class="custom-tree-node" slot-scope="{ data }">
          {{ data.name }}
        </span>
      </el-tree> -->
    </div>
    <div class="dialog-footer" slot="footer">
      <div class="tg-tree--choose">
        <span
          >已选择校区数量：<span>{{ checkedNums }}</span></span
        >
      </div>
      <div>
        <el-button type="plain" @click="handleClose" class="tg-button--plain"
          >取消</el-button
        >
        <el-button type="primary" @click="really" class="tg-button--primary"
          >确定</el-button
        >
      </div>
    </div>
  </el-dialog>
</template>
<script>
import organizationApi from "@/api/organization";
export default {
  data() {
    return {
      all_checked: false,
      isIndeterminate: false,
      checked: true,
      school_data: [],
      school_data_temp: [],
      // ids_prop: [
      //   "4c9d3c21-c2da-4fb5-aa55-2874a6a0d825",
      //   "e7c12eb9-851a-47da-961f-87c93fae9381"
      // ],
      // new_add_ids: [],
      // checkedNums:"",
      filterText: ""
    };
  },
  props: {
    ids: Array,
    title: String
  },
  watch: {},
  mounted() {
    this.getSchoolTree();
  },
  computed: {
    checkedNums() {
      let n = 0;
      this.school_data_temp.map((item) => {
        item.child.map((child) => {
          if (child.checked) {
            n++;
          }
        });
      });
      return n;
    },
    all_school_num() {
      let n = 0;
      this.school_data_temp.map((item) => {
        item.child.map(() => {
          n++;
        });
      });
      return n;
    },
    new_add_ids() {
      const arr = [];
      this.school_data_temp.map((item) => {
        item.child.map((child) => {
          if (child.checked && !child.disabled) {
            arr.push(child.id);
          }
        });
      });
      return arr;
    }
  },
  methods: {
    handleCheckAllChange(check) {
      this.school_data_temp.map((item) => {
        item.child.map((child) => {
          if (check) {
            child.checked = true;
          } else {
            if (!child.disabled) {
              child.checked = false;
            }
          }
        });
      });
    },
    expanded(area, event) {
      area.expanded = !area.expanded;
      // eslint-disable-next-line no-undef
      $(event.target).parents(".leaf").children(".content").slideToggle();
    },
    searchVal() {
      const { filterText, school_data_temp } = this;
      if (filterText) {
        school_data_temp.map((item) => {
          item.is = false;
          item.child.map((child) => {
            if (child.name.indexOf(filterText) > -1) {
              item.is = true;
              child.isHide = false;
              item.isHide = false;
            } else {
              item.isHide = !item.is;
              child.isHide = true;
            }
          });
        });
      } else {
        school_data_temp.map((item) => {
          item.child.map((child) => {
            item.isHide = false;
            child.isHide = false;
          });
        });
      }
      // this.$set();
      this.$forceUpdate();
      const { all_school_num, checkedNums } = this;
      this.all_checked = all_school_num === checkedNums;
    },
    handleClose() {
      // this.search = {
      //   partName: "",
      //   schoolName: ""
      // };
      this.$emit("close");
    },
    set_property() {
      this.school_data.map((item, i) => {
        item.child.map((child, j) => {
          const obj = Object.assign({}, child, {
            checked: false,
            disabled: false
          });
          this.$set(this.school_data[i].child, j, obj);
        });
        this.$set(this.school_data[i], "expanded", true); // 初始全展开
      });
    },
    init_school_data() {
      this.set_property();
      this.school_data.map((item) => {
        item.child.map((child) => {
          this.ids.map((id) => {
            if (child.id === id) {
              Object.assign(child, {
                checked: true,
                disabled: true
              });
            }
          });
        });
      });
      this.school_data_temp = this.school_data;
    },

    getSchoolTree() {
      organizationApi.getSchoolPart({}).then((res) => {
        if (res.data && res.data.school_data) {
          this.school_data = res.data.school_data;
          this.init_school_data();
        }
      });
    },
    really() {
      this.$emit("comfirm", this.new_add_ids);
    },

    checkChange() {
      const { all_school_num, checkedNums } = this;
      this.all_checked = all_school_num === checkedNums;
    }
  }
};
</script>
<style lang="less" scoped>
.time-setting-dialog {
  .school-tree {
    padding: 8px 0;
    border-top: 1px solid #ecedf2;
  }
  ::v-deep .el-dialog__body {
    padding: 16px;
  }
  .tg-dialog__content {
    height: 557px;
  }
  .dialog-footer {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    height: 32px;
  }

  .tg-tree--choose {
    font-size: 14px;
    span {
      color: #2d80ed;
      span {
        color: #333;
      }
    }
  }

  ::v-deep .el-form-item__content {
    line-height: 32px;
  }
}

.tg-tree--custom {
  margin-top: 16px;
  height: calc(100% - 32px);
  overflow: auto;
  .leaf {
    margin-bottom: 16px;
    .title {
      background-color: #f5f8fc;
      height: 40px;
      border: 1px solid #2d80ed;
      border-radius: 4px;
      padding: 10px 15px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      cursor: pointer;
      .expanded {
        background-image: url("../../assets/图片/icon_plus.png");
        width: 16px;
        height: 16px;
        display: block;
        background-size: cover;
        margin-right: 20px;
        &.active {
          background-image: url("../../assets/图片/icon_minus.png");
        }
      }
      .pos__icon {
        background-image: url("../../assets/图片/icon_position.png");
        width: 10px;
        height: 14px;
        display: block;
        background-size: cover;
        margin-right: 5px;
      }
      .name {
        font-size: 14px;
        font-weight: 500;
        margin-right: 5px;
      }
      .num {
        font-size: 14px;
        color: #8492a6;
      }
    }
    .content {
      margin-top: 12px;
      margin-left: 22px;
      padding: 6px 0 4px 26px;
      border-left: 2px solid #e7f0f8;
      // display: none;
      overflow: hidden;
      /deep/ .el-checkbox {
        margin-right: 10px;
        padding-bottom: 12px;
        width: 175px;
      }
    }
  }
  .no-data {
    text-align: center;
    color: #c1c3c5;
  }
}
</style>
