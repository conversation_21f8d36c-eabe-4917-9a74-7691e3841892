<template>
  <el-dialog
    title="添加上课时间"
    :visible="true"
    width="796px"
    class="add-class-time"
    :before-close="back"
    custom-class="add-class-time"
    append-to-body
  >
    <div class="class-time__wrap">
      <div class="class-time">
        <div class="class-time__label">上课时间</div>
      </div>
      <div class="class-time__content">
        <div class="class-time__content--top">
          <el-checkbox
            style="margin-top: 16px; margin-left: 16px"
            :indeterminate="work_is_indeterminate"
            v-model="checkWorkDay"
            @change="handleCheckWorkDayChange"
            >工作日</el-checkbox
          >
          <el-checkbox
            style="margin-top: 16px"
            :indeterminate="weekend_is_indeterminate"
            v-model="checkWeekend"
            @change="handleCheckWeekendChange"
            >周末</el-checkbox
          >
        </div>
        <div class="class-time__content--bottom">
          <el-checkbox-group
            style="margin-top: 16px; margin-left: 16px"
            v-model="checkedWeeks"
            @change="handleCheckedWeeksChange"
          >
            <el-checkbox v-for="week in Weeks" :label="week.id" :key="week.id"
              >{{ week.name }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
    </div>
    <!-- 常用时间 -->
    <el-row
      style="margin-top: 16px"
      v-has="{ m: 'course', o: 'commonTimePeriod' }"
    >
      <el-col :span="3">
        <div style="margin-top: 6px">常用时间段</div>
      </el-col>
      <el-col :span="20">
        <el-select
          placeholder="请选择时间段"
          v-model="class_time"
          @change="selectClassTime"
          :popper-append-to-body="false"
        >
          <el-option
            v-for="(item, index) in classTime_list"
            :key="index"
            :label="item.start_time + ' - ' + item.end_time"
            :value="index"
          ></el-option>
        </el-select>
      </el-col>
    </el-row>
    <div class="time-selection">
      <el-row class="tg-box--margin">
        <el-col :span="3">
          <div style="margin-top: 5px; margin-left: 16px">开始时间</div>
        </el-col>
        <el-col :span="14">
          <div>
            <el-time-picker
              v-model="startTime"
              format="HH:mm"
              value-format="timestamp"
              @change="handleStartTimeChange"
              placeholder="任意时间点"
            >
            </el-time-picker>
            <span style="margin: 0 5px 0 5px">至</span>
            <el-time-picker
              :disabled="isEndTimeDisabled"
              :picker-options="{
                selectableRange: `${
                  startTimestamp ? startTimestamp : '00:00'
                }:00  - 23:49:49`
              }"
              v-model="endTime"
              format="HH:mm"
              value-format="HH:mm"
              @change="handleEndTimeChange"
              placeholder="任意时间点"
            >
            </el-time-picker>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="3" style="margin-top: 12px">
          <div style="margin-top: 5px; margin-left: 16px">上课时长</div>
        </el-col>
        <el-col :span="14">
          <div>
            <el-input
              style="margin-top: 12px; width: 300px"
              v-model="durationTime"
              placeholder="上课时长"
              readonly
            ></el-input>
          </div>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="3" style="margin-top: 12px">
          <div style="margin-top: 5px; margin-left: 16px">课程形式</div>
        </el-col>
        <el-col :span="6">
          <div>
            <el-select
              style="margin-top: 12px"
              v-model="ruleForm.scheduling_form"
              @change="selectRooms"
              placeholder="请选择"
              :popper-append-to-body="false"
            >
              <el-option
                v-for="(
                  value, key, index
                ) in school_service_scheduling_map_form"
                :key="index"
                :value="key"
                :label="value"
              >
              </el-option>
            </el-select>
          </div>
        </el-col>

        <el-col
          :span="9"
          style="display: flex"
          v-if="ruleForm.scheduling_form === 'offline'"
        >
          <div style="margin-top: 18px; margin-left: 16px">上课教室</div>
          <div>
            <el-select
              style="margin-top: 12px; margin-left: 15px"
              v-model="ruleForm.school_room_id"
              @change="selectRoom"
              placeholder="请选择"
              :popper-append-to-body="false"
              class="choose-room"
            >
              <template v-for="item in schoolroom_list">
                <el-option
                  v-if="item.is_enabled"
                  :key="item.name"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </template>
            </el-select>
          </div>
        </el-col>
      </el-row>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="back" class="tg-button--plain">取消</el-button>
      <el-button
        type="primary"
        @click="dialogVisibleTT"
        class="tg-button--primary"
        >确定</el-button
      >
    </span>
  </el-dialog>
</template>
<script>
import { getCourseRuleTimeInfo } from "@/api/courseManagement.js";
import moment from "moment";
const weekOptions = [
  { id: "1", name: "星期一" },
  { id: "2", name: "星期二" },
  { id: "3", name: "星期三" },
  { id: "4", name: "星期四" },
  { id: "5", name: "星期五" },
  { id: "6", name: "星期六" },
  { id: "0", name: "星期日" }
];
export default {
  props: {
    list: Array,
    courseId: String,
    departmentId: [String, Array],
    departmentName: String,
    classroom_type: String
  },
  data() {
    return {
      ruleMinute: 0,
      minutesTimestamp: 0,
      startTimestamp: 0,
      isEndTimeDisabled: true,
      startTime: "",
      endTime: "",
      class_time: "",
      readonly: true,
      ruleForm: {
        scheduling_form: "",
        duration: "",
        school_room_name: "",
        school_room_id: ""
      },
      durationTime: "",
      checkedWeeks: [],
      value1: "",
      checkWorkDay: false,
      work_is_indeterminate: false,
      checkWeekend: false,
      weekend_is_indeterminate: false,
      Weeks: weekOptions,
      isIndeterminate: true,
      values: "",
      value: "" // 时间选择器
    };
  },
  computed: {
    // 获取教室列表
    schoolroom_list() {
      return this.$store.getters.doneGetSchoolroomList;
    },
    // 获取教室列表
    classTime_list() {
      return this.$store.getters.doneGetClassTimeList;
    },

    // 获取教室列表
    school_service_scheduling_map_form() {
      return this.$store.getters.doneGetSchoolServiceSchedulingMapForm;
    }
  },
  mounted() {},
  methods: {
    convertTimeToTimestamp(time) {
      const now = new Date();
      const [hours, minutes] = time.split(":").map(Number);
      now.setHours(hours, minutes, 0, 0); // 设置当天的指定时间
      return now.getTime(); // 返回秒级时间戳
    },
    selectClassTime() {
      const toFormat = (time) => {
        return moment(time).format("HH:mm");
      };
      const startTime = this.convertTimeToTimestamp(
        this.classTime_list[this.class_time].start_time
      );
      this.startTime = startTime;
      const now = new Date(startTime);
      now.setMinutes(now.getMinutes() + 1);
      this.startTimestamp = toFormat(now.getTime());
      this.endTime = this.classTime_list[this.class_time].end_time;
      console.log([this.startTime, this.endTime]);
      this.selectTimeRange([toFormat(this.startTime), this.endTime]);
    },
    selectRooms() {},

    selectRoom(val) {
      this.schoolroom_list.map((item) => {
        if (item.id === val) {
          this.ruleForm.school_room_name = item.name;
        }
      });
    },
    minutesBetween(startTime, endTime) {
      function timeToMinutes(time) {
        const parts = time.split(":");
        return parseInt(parts[0], 10) * 60 + parseInt(parts[1], 10);
      }
      const startMinutes = timeToMinutes(startTime);
      const endMinutes = timeToMinutes(endTime);
      const difference = endMinutes - startMinutes;
      return difference > 0 ? difference : 24 * 60 + difference;
    },
    selectTimeRange(value) {
      if (!value) {
        this.durationTime = "";
        return;
      }
      const diffInMinutes = this.minutesBetween(value[0], value[1]);
      // 将分钟数转换为小时和分钟
      const hours = Math.floor(diffInMinutes / 60);
      const minutes = diffInMinutes % 60;
      this.durationTime =
        (hours ? hours + "小时" : "") + (minutes ? minutes + "分钟" : "");
      console.log(this.durationTime);
      this.ruleForm.duration = (hours || 0) * 3600 + (minutes || 0) * 60;
      console.log(this.ruleForm.duration);
    },
    getTimeInMinutes(timeString) {
      const [hh, mm] = timeString.split(":").map((num) => parseInt(num));
      return hh * 60 + mm;
    },
    handleCheckWeekendChange(val) {
      if (val) {
        this.checkedWeeks.push("0", "6");
      } else {
        this.checkedWeeks = this.checkedWeeks.filter((item) => {
          return !["0", "6"].includes(item);
        });
      }
      this.weekend_is_indeterminate = false;
    },
    handleCheckWorkDayChange(val) {
      if (val) {
        this.checkedWeeks.push("1", "2", "3", "4", "5");
      } else {
        this.checkedWeeks = this.checkedWeeks.filter((item) => {
          return !["1", "2", "3", "4", "5"].includes(item);
        });
      }
      this.work_is_indeterminate = false;
    },
    handleCheckedWeeksChange() {
      // let checkedCount = value.length;

      this.checkWeekend =
        this.checkedWeeks.length > 1 &&
        this.checkedWeeks.indexOf("0") > -1 &&
        this.checkedWeeks.indexOf("6") > -1;
      this.checkWorkDay =
        this.checkedWeeks.length >= 5 &&
        this.checkedWeeks.indexOf("1") > -1 &&
        this.checkedWeeks.indexOf("2") > -1 &&
        this.checkedWeeks.indexOf("3") > -1 &&
        this.checkedWeeks.indexOf("4") > -1 &&
        this.checkedWeeks.indexOf("5") > -1;

      const work = this.checkedWeeks.filter((item) => item <= 5 && item > 0);
      const week = this.checkedWeeks.filter((item) => item > 5 || item === 0);
      this.weekend_is_indeterminate = week.length > 0 && week.length < 2;
      this.work_is_indeterminate = work.length > 0 && work.length < 5;
    },

    back() {
      this.$emit("close", false);
      this.form = {};
    },
    dialogVisibleTT() {
      const toFormat = (time) => {
        return moment(time).format("HH:mm");
      };
      this.ruleForm.start_time = toFormat(this.startTime) || null;
      this.ruleForm.end_time = this.endTime || null;
      let course_list = [];
      let isConflict = false;
      this.checkedWeeks.map((item) => {
        course_list.push(Object.assign({}, this.ruleForm, { week_day: item }));
      });
      console.log(this.checkedWeeks && this.checkedWeeks.length > 0);
      if (!(this.checkedWeeks && this.checkedWeeks.length > 0)) {
        this.$message.info("请选择上课时间");
        return;
      }
      if (this.startTime === "" && this.endTime === "") {
        this.$message.info("请选择常用时间段或开始时间");
        return;
      }
      if (!this.ruleForm.scheduling_form) {
        this.$message.info("请选择课程形式");
        return;
      }

      if (this.list.length > 0) {
        this.list.map((item) => {
          if (this.checkedWeeks.includes(item.week_day)) {
            course_list.map((week) => {
              if (week.week_day === item.week_day) {
                if (
                  week.start_time === item.start_time ||
                  week.end_time === item.end_time
                ) {
                  isConflict = true;
                }
              }
            });
          }
        });
      }
      const unique = (origin) => {
        const result = [];
        for (let i = 0; i < origin.length; i++) {
          const cur = origin[i];
          console.log(result.find((k) => k.week_day !== cur.week_day));
          if (!result.find((k) => k.week_day === cur.week_day)) {
            result.push(cur);
          }
        }
        return result;
      };
      course_list = unique(course_list);
      if (isConflict) {
        this.$message.error("与已添加的时段有冲突");
      }
      console.log("67672637 :>> ", 67672637);
      if (
        !isConflict &&
        this.ruleForm.start_time != null &&
        this.ruleForm.end_time != null
      ) {
        this.checkedWeeks = [];
        this.$emit("add", course_list);
        this.$emit("close", false);
      }
    },
    minutesToTimestamp(minutes) {
      const date = new Date(); // 当前日期和时间
      date.setMinutes(minutes); // 设置指定的分钟数
      return date.getTime(); // 获取对应的时间戳
    },
    async getCourseRuleDuration() {
      const res = await getCourseRuleTimeInfo({
        course_id: this.courseId
      });
      if (res.data) {
        this.ruleMinute = res.data.lesson_duration;
        this.minutesTimestamp = this.minutesToTimestamp(
          res.data.lesson_duration
        );
      }
    },
    handleStartTimeChange(val) {
      const toFormat = (time) => {
        return moment(time).format("HH:mm");
      };
      const now = new Date(val);
      now.setMinutes(now.getMinutes() + 1);
      this.startTimestamp = toFormat(now.getTime());
      console.log(this.startTimestamp);
      if (val) {
        this.endTime = this.addFiveMinutes(toFormat(val));
      }
      if (this.startTime && this.endTime) {
        this.selectTimeRange([toFormat(this.startTime), this.endTime]);
      }
    },
    handleEndTimeChange() {
      const toFormat = (time) => {
        return moment(time).format("HH:mm");
      };
      if (this.startTime && this.endTime) {
        this.selectTimeRange([toFormat(this.startTime), this.endTime]);
      }
    },
    addFiveMinutes(timeStr) {
      const parts = timeStr.split(":");
      const hours = parseInt(parts[0], 10);
      const minutes = parseInt(parts[1], 10);
      const addMinute = this.ruleMinute;
      // 创建Date对象
      const date = new Date();
      date.setHours(hours);
      date.setMinutes(minutes);
      date.setMinutes(date.getMinutes() + addMinute);
      // 返回格式化后的时间字符串
      return (
        date.getHours().toString().padStart(2, "0") +
        ":" +
        date.getMinutes().toString().padStart(2, "0")
      );
    }
  },
  created() {
    if (this.list.length > 0) {
      this.list.map(() => {
        // this.checkedWeeks.push(item.week_day);
      });
      this.handleCheckedWeeksChange();
    }
    // 常规班受权限控制
    if (this.classroom_type === "normal") {
      if (this.$_has({ m: "course", o: "infoTime" })) {
        this.isEndTimeDisabled = false;
      }
    } else {
      this.isEndTimeDisabled = false;
    }
    this.getCourseRuleDuration();
    this.$store.dispatch("getSchoolroomList", {
      department_id: this.departmentId
    });
    this.$store.dispatch("getClassTimeList", {
      is_public: true,
      department_id: this.departmentId
    });
    this.$store.dispatch("getSchoolServiceSchedulingMapForm", {});
  }
};
</script>
<style lang="less" scoped>
.class-time {
  width: 160px;
  height: 106px;
  background: #ebf4ff;
  border: 1px dashed #2d80ed;
  border-radius: 4px 0 0 4px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.class-time__label {
  font-size: 14px;
  color: #475669;
  letter-spacing: 0;
}
.class-time__content {
  height: 106px;
  width: calc(100% - 162px);
  background: #ffffff;
  border: 1px solid #2d80ed;
  border-left: none;
  box-shadow: 0 2px 6px 0 rgba(204, 208, 217, 0.35);
  border-radius: 0 4px 4px 0;
}
.class-time__content--top {
  width: 100%;
  height: 50%;
  border-bottom: 1px solid #e9f0f7;
}
.class-time__content--bottom {
  width: 100%;
  height: 50%;
}
.time-selection {
  height: 192px;
  margin-top: 12px;
  border: 1px solid #2d80ed;
  border-radius: 4px;
}
.add-class-time {
  width: 820px;
  margin: 3% auto;
  .choose-room ::v-deep .el-select-dropdown {
    width: 168px;
  }
  ::v-deep .el-dialog__body {
    padding: 16px;
    overflow: inherit;
  }
}
::v-deep .el-checkbox {
  margin-right: 15px;
}
.class-time__wrap {
  display: flex;
  flex-direction: row;
}
</style>
