<template>
  <div class="choice-issue-list">
    <div
      v-for="(item, index) in option_list"
      :key="index"
      @click="check_answer(item)"
      :class="[
        'choice-issue-option',
        user_answer === ''
          ? 'choice-issue-option-default'
          : answer_result === 'is_right' && user_answer === Object.keys(item)[0]
          ? 'choice-issue-option-true'
          : answer_result === 'is_wrong' && user_answer === Object.keys(item)[0]
          ? 'choice-issue-option-false'
          : answer_result === 'is_wrong' &&
            issue_answer === Object.keys(item)[0] &&
            show_answer === false
          ? 'choice-issue-option-true'
          : show_answer === true && issue_answer === Object.keys(item)[0]
          ? 'choice-issue-option-true'
          : 'choice-issue-option-default'
      ]"
    >
      <i class="option-icon"> </i>
      <div class="option-text">{{ Object.values(item)[0] }}</div>
    </div>
  </div>
</template>
<script>
export default {
  name: "choice-issue",
  props: {
    issue_answer: {
      // 题目答案
      type: String,
      default: ""
    },
    option_list: {
      default: ""
    },
    show_answer: {
      // 是否展示答案
      type: <PERSON>olean
    },
    is_redo: {
      type: Boolean
    },
    user_answer: {
      default: ""
    },
    answer_result: {
      default: ""
    }
  },
  watch: {
    is_redo: {
      handler: function (data) {
        if (data === true) {
          this.$emit("reset");
        }
      },
      deep: true
    }
  },
  data() {
    return {};
  },
  methods: {
    check_answer: function (choice) {
      if (this.answer_result === "not_answer") {
        if (choice) {
          if (Object.keys(choice)[0] === this.issue_answer) {
            this.$emit("change_answer_result", {
              user_answer: Object.keys(choice)[0],
              answer_result: "is_right"
            });
          } else {
            this.$emit("change_answer_result", {
              user_answer: Object.keys(choice)[0],
              answer_result: "is_wrong"
            });
          }
        }
      }
    }
  }
};
</script>
<style lang="less" scoped>
.choice-issue-list {
  width: 100%;
  height: 424px;
  box-sizing: border-box;
  .choice-issue-option {
    line-height: 45px;
    width: 100%;
    height: 45px;
    border-radius: 36px;
    box-sizing: border-box;
    text-align: center;
    position: relative;
    margin-bottom: 12px;
    cursor: pointer;
    border: 2px solid #e4e5e6;

    .option-icon {
      width: 40px;
      height: 40px;
      line-height: 40px;
      border-radius: 50%;
      position: absolute;
      top: 50%;
      margin-top: -20px;
      left: 8px;
      font-style: normal;
      font-weight: 700;
      font-size: 24px;
      color: #3b3b3b;
      text-align: center;
    }
    .icon-default {
      background-color: #f1f1f1;
    }
    .icon-close {
      // background: url("@/assets/exerciseDetail/差.png") no-repeat;
      background-size: contain;
      background-color: #ffffff;
    }
    .icon-check {
      // background: url("@/assets/exerciseDetail/勾.png") no-repeat;
      background-size: contain;
      background-color: #54d773;
    }
    .option-text {
      font-size: 2.4vh;
      color: #717883;
      text-align: center;
    }
  }
  .choice-issue-option-false {
    background-color: #ff6667;
    .option-text {
      color: #ffffff;
    }
    .option-icon {
      // background: url("@/assets/exerciseDetail/差.png") no-repeat;
      background-size: contain;
      background-color: #ffffff;
    }
  }
  .choice-issue-option-true {
    background-color: #54d773;
    .option-text {
      color: #ffffff;
    }
    .option-icon {
      // background: url("@/assets/exerciseDetail/勾.png") no-repeat;
      background-size: contain;
      background-color: #54d773;
    }
  }
  .choice-issue-option-default {
    background: #f6f8fb;
    .option-text {
      color: #717883;
    }
    .option-icon {
      background-color: transparent;
    }
  }
}
</style>
