<template>
  <div class="tg-pagination">
    <span class="el-pagination__total">共 {{ total }} 条</span>
    <div class="page">
      <el-pagination
        :background="background"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :layout="layout"
        :page-sizes="pageSizes"
        :total="total"
        v-bind="$attrs"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: "Pagination",
  props: {
    total: {
      required: true,
      type: Number,
      default: 0
    },
    page: {
      type: Number,
      default: 1
    },
    limit: {
      type: Number,
      default: 10
    },
    pageSizes: {
      type: Array,
      default() {
        return [10, 20, 30, 50];
      }
    },
    layout: {
      type: String,
      default: "total, sizes, prev, pager, next, jumper"
    },
    background: {
      type: Boolean,
      default: true
    },
    autoScroll: {
      type: <PERSON>olean,
      default: true
    },
    hidden: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    }
  },
  computed: {
    currentPage: {
      get() {
        return this.page;
      },
      set(val) {
        this.$emit("update:page", val);
      }
    },
    pageSize: {
      get() {
        return this.limit;
      },
      set(val) {
        this.$emit("update:limit", val);
      }
    }
  },
  methods: {
    handleSizeChange(val) {
      this.$emit("pagination", { page: 1, limit: val });
    },
    handleCurrentChange(val) {
      this.$emit("pagination", { page: val, limit: this.pageSize });
    }
  }
};
</script>

<style lang="less" scoped>
.pagination-container {
  background: #fff;
  padding: 32px 16px;
}
.tg-pagination {
  border-top: 1px solid #e0e6ed;
  // margin-top: 30px;
  .el-pagination__total {
    white-space: nowrap;
  }
}
.page {
  ::v-deep .el-pagination__total {
    display: none;
  }
}
</style>
