<!--选择周以及时间段--多选组件-->
<template>
  <div class="week-time-frame">
    <div v-if="check_list.length === 0">
      <el-input
        class="week-time-frame--placeholder tg-select--dialog"
        :placeholder="placeholder"
        @click.native="openDialog"
        :disabled="is_disabled"
        @mouseenter.native="hover_flag = true"
        @mouseleave.native="hover_flag = false"
        readonly
      >
        <i
          v-if="img_type === 'arrow'"
          slot="suffix"
          class="el-select__caret el-input__icon el-icon-arrow-down arrow"
        ></i>
        <img
          :src="
            !hover_flag
              ? require('@/assets/图片/icon_more.png')
              : require('@/assets/图片/icon_more_ac.png')
          "
          alt=""
          slot="suffix"
          v-if="img_type === 'dotted'"
          class="btn__img--dotted"
          :class="{ disabled: is_disabled }"
        />
        <img
          src="@/assets/图片/icon_add_circle.png"
          alt=""
          slot="suffix"
          v-if="img_type === 'circle'"
          class="btn__img--circle"
        />
        <img
          src="@/assets/图片/icon_add_square.png"
          alt=""
          slot="suffix"
          v-if="img_type === 'square'"
          class="btn__img--square"
        />
      </el-input>
    </div>
    <div
      class="permission-select tg-select--dialog"
      :class="[
        { red: has_error, disabled: is_disabled },
        hover_flag ? 'border--active' : ''
      ]"
      @click.stop="openDialog"
      @mouseenter="hover_flag = true"
      @mouseleave="hover_flag = false"
      v-else
    >
      <div class="permission-select__inner">
        <div
          class="permission-tag"
          v-for="(item, index) in check_list"
          :key="index"
          :class="{ disabled: is_disabled }"
        >
          <span>{{ item.fullName }}</span>
          <img
            src="@/assets/图片/icon_close_green.png"
            alt=""
            @click.stop="inputDel(index)"
            :class="{ disabled: is_disabled }"
          />
        </div>
      </div>
      <i
        v-if="img_type === 'arrow'"
        class="el-select__caret el-input__icon el-icon-arrow-down arrow"
      ></i>
      <img
        :src="
          !hover_flag
            ? require('@/assets/图片/icon_more.png')
            : require('@/assets/图片/icon_more_ac.png')
        "
        alt=""
        v-if="img_type === 'dotted'"
        class="btn__img--dotted"
        :class="{ disabled: is_disabled }"
      />
      <img
        src="@/assets/图片/icon_add_circle.png"
        alt=""
        v-if="img_type === 'circle'"
        class="btn__img--circle"
      />
      <img
        src="@/assets/图片/icon_add_square.png"
        alt=""
        v-if="img_type === 'square'"
        class="btn__img--square"
      />
    </div>
    <el-dialog
      title="添加上课时间"
      :visible="true"
      width="796px"
      v-if="week_time_visible"
      class="add-class-time"
      :modal="has_modal"
      :append-to-body="has_modal"
      :before-close="back"
      custom-class="add-class-time"
    >
      <div class="class-time__wrap">
        <div class="class-time">
          <div class="class-time__label">上课时间</div>
        </div>
        <div class="class-time__content">
          <div class="class-time__content--top">
            <el-checkbox
              style="margin-top: 16px; margin-left: 16px"
              :indeterminate="work_is_indeterminate"
              v-model="checkWorkDay"
              @change="handleCheckWorkDayChange"
              >工作日</el-checkbox
            >
            <el-checkbox
              style="margin-top: 16px"
              :indeterminate="weekend_is_indeterminate"
              v-model="checkWeekend"
              @change="handleCheckWeekendChange"
              >周末</el-checkbox
            >
          </div>
          <div class="class-time__content--bottom">
            <el-checkbox-group
              style="margin-top: 16px; margin-left: 16px"
              v-model="checkedWeeks"
              @change="handleCheckedWeeksChange"
            >
              <el-checkbox v-for="week in Weeks" :label="week.id" :key="week.id"
                >{{ week.name }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
      </div>
      <div class="time-selection">
        <el-row class="tg-box--margin">
          <el-col :span="3">
            <div style="margin-top: 5px; margin-left: 16px">开始时间</div>
          </el-col>
          <el-col :span="14">
            <div>
              <el-time-picker
                style="width: 300px"
                is-range
                v-model="value1"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                placeholder="选择时间范围"
                format="HH:mm"
                value-format="HH:mm"
              >
              </el-time-picker>
            </div>
          </el-col>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="back" class="tg-button--plain">取消</el-button>
        <el-button
          type="primary"
          @click="dialogVisibleTT"
          class="tg-button--primary"
          >确定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
const weekOptions = [
  { id: "1", name: "周一" },
  { id: "2", name: "周二" },
  { id: "3", name: "周三" },
  { id: "4", name: "周四" },
  { id: "5", name: "周五" },
  { id: "6", name: "周六" },
  { id: "0", name: "周日" }
];

export default {
  name: "weekTimeFrame",
  data() {
    return {
      check_list: [],
      week_time_visible: false,
      hover_flag: false,
      loading: false,

      readonly: true,
      ruleForm: {
        duration: "",
        school_room_name: "",
        school_room_id: ""
      },
      checkedWeeks: [],
      value1: "",
      checkWorkDay: false,
      work_is_indeterminate: false,
      checkWeekend: false,
      weekend_is_indeterminate: false,
      Weeks: weekOptions
    };
  },
  props: {
    check_week: Array,
    check_time: Array,
    format_check: Array,
    obj: Object,
    img_type: {
      type: String,
      default: "dotted"
    },
    department_id: {},
    has_error: Boolean,
    placeholder: String,
    is_disabled: {
      type: Boolean,
      default: false
    },
    has_modal: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    school_id() {
      return this.$store.getters.doneGetSchoolId;
    }
  },
  watch: {
    check_week: {
      handler(val) {
        console.log(val);
        if (!val) {
          this.checkWorkDay = false;
          this.work_is_indeterminate = false;
          this.checkWeekend = false;
          this.weekend_is_indeterminate = false;
          this.value1 = "";
          this.checkedWeeks = [];
          this.check_list = [];
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    inputDel(index) {
      if (this.is_disabled) return;
      this.check_list.splice(index, 1);
      const ids = this.check_list.map((item) => item.id);
      console.log(this.check_list);
      this.$emit("update:check_week", ids);
      this.$emit(
        "update:check_time",
        this.check_list.length ? this.check_list[0].time : []
      );
    },
    openDialog() {
      if (this.is_disabled) return;
      this.week_time_visible = true;
    },

    handleCheckWeekendChange(val) {
      if (val) {
        this.checkedWeeks.push("0", "6");
      } else {
        this.checkedWeeks = this.checkedWeeks.filter((item) => {
          return !["0", "6"].includes(item);
        });
      }
      this.weekend_is_indeterminate = false;
    },
    handleCheckWorkDayChange(val) {
      if (val) {
        this.checkedWeeks.push("1", "2", "3", "4", "5");
      } else {
        this.checkedWeeks = this.checkedWeeks.filter((item) => {
          return !["1", "2", "3", "4", "5"].includes(item);
        });
      }
      this.work_is_indeterminate = false;
    },
    handleCheckedWeeksChange() {
      this.checkWeekend =
        this.checkedWeeks.length > 1 &&
        this.checkedWeeks.indexOf("0") > -1 &&
        this.checkedWeeks.indexOf("6") > -1;
      this.checkWorkDay =
        this.checkedWeeks.length >= 5 &&
        this.checkedWeeks.indexOf("1") > -1 &&
        this.checkedWeeks.indexOf("2") > -1 &&
        this.checkedWeeks.indexOf("3") > -1 &&
        this.checkedWeeks.indexOf("4") > -1 &&
        this.checkedWeeks.indexOf("5") > -1;

      const work = this.checkedWeeks.filter((item) => item <= 5 && item > 0);
      const week = this.checkedWeeks.filter((item) => item > 5 || item === 0);
      this.weekend_is_indeterminate = week.length > 0 && week.length < 2;
      this.work_is_indeterminate = work.length > 0 && work.length < 5;
    },

    back() {
      this.$emit("close", false);
      this.week_time_visible = false;
    },
    dialogVisibleTT() {
      this.ruleForm.start_time = this.value1[0] || null;
      this.ruleForm.end_time = this.value1[1] || null;
      if (!(this.checkedWeeks && this.checkedWeeks.length > 0)) {
        this.$message.info("请选择上课时间");
        return;
      }
      if (this.value1 === "") {
        this.$message.info("请选择开始时间");
        return;
      }
      const formatWeek = this.checkedWeeks.map((i) => ({
        id: this.Weeks.find((j) => j.id === i).id,
        name: this.Weeks.find((j) => j.id === i).name,
        time: this.value1,
        fullName:
          this.Weeks.find((j) => j.id === i).name +
          ` ${this.value1[0]}-${this.value1[1]}`
      }));
      this.$emit("update:check_week", this.checkedWeeks);
      this.$emit("update:check_time", this.value1);
      this.$emit("update:format_check", formatWeek);
      this.check_list = formatWeek;
      this.week_time_visible = false;
    }
  }
};
</script>
<style lang="less" scoped>
.week-time-frame {
  .permission-select {
    width: 240px;
    // overflow-y: auto;
    cursor: pointer;
    height: 32px;
    box-sizing: border-box;
    border: 1px solid @input-border_color;
    border-radius: 4px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    position: relative;
    i {
      cursor: pointer;
    }
    .permission-select__inner {
      width: calc(100% - 48px);
      display: flex;
      flex-direction: row;
      padding: 0 16px;
      overflow-y: auto;
      .permission-tag + .permission-tag {
        margin-left: 16px;
      }
      .permission-tag {
        border-radius: 4px;
        border: 1px solid @base-color;
        background-color: #ebf4ff;
        display: flex;
        flex-direction: row;
        height: 20px;
        line-height: 20px;
        align-items: center;
        cursor: pointer;
        &.disabled {
          cursor: not-allowed;
        }
        span {
          padding: 0 16px;
          color: @text-color_second;
          font-family: @text-famliy_medium;
          font-size: @text-size_normal;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        img {
          width: 14px;
          height: 14px;
          margin-right: 16px;
          cursor: pointer;
          &.disabled {
            cursor: not-allowed;
          }
        }
      }
    }
  }
  .el-select__caret {
    color: #c0ccda;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .btn__img--circle {
    width: 16px;
    height: 16px;
    margin-right: 16px;
    cursor: pointer;
  }
  .btn__img--square {
    width: 14px;
    height: 14px;
    margin-right: 16px;
    cursor: pointer;
  }
  .week-time-frame--placeholder {
    ::v-deep .el-input__inner {
      cursor: pointer;
    }
  }
  .disabled {
    background-color: #f5f7fa;
    border-color: transparent;
    cursor: not-allowed;
    .tips {
      color: #c0ccda;
    }
    &:hover {
      border-color: @base-color;
    }
  }
  .btn__img--dotted {
    margin-right: 14px;
    margin-left: 10px;
    &.disabled {
      cursor: not-allowed;
    }
  }
  .border--active {
    position: relative;
    &::after {
      content: "";
      position: absolute;
      width: calc(100% + 2px);
      height: 32px;
      left: -3px;
      top: -3px;
      border: none;
      border-radius: 6px;
      z-index: 10;
      pointer-events: none;
    }
    border-color: @base-color;
  }
  .week-time-frame--placeholder {
    position: relative;
    &:hover {
      &::after {
        content: "";
        position: absolute;
        width: 100%;
        height: 32px;
        left: -2px;
        top: -2px;
        border: none;
        border-radius: 6px;
        z-index: 10;
        pointer-events: none;
      }
      ::v-deep .el-input__inner {
        border: 1px solid @base-color;
      }
    }
  }
}
.class-time {
  width: 160px;
  height: 106px;
  background: #ebf4ff;
  border: 1px dashed #2d80ed;
  border-radius: 4px 0 0 4px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.class-time__label {
  font-size: 14px;
  color: #475669;
  letter-spacing: 0;
}
.class-time__content {
  height: 106px;
  width: calc(100% - 162px);
  background: #ffffff;
  border: 1px solid #2d80ed;
  border-left: none;
  box-shadow: 0 2px 6px 0 rgba(204, 208, 217, 0.35);
  border-radius: 0 4px 4px 0;
}
.class-time__content--top {
  width: 100%;
  height: 50%;
  border-bottom: 1px solid #e9f0f7;
}
.class-time__content--bottom {
  width: 100%;
  height: 50%;
}
.time-selection {
  height: 192px;
  margin-top: 12px;
  border: 1px solid #2d80ed;
  border-radius: 4px;
}
.add-class-time {
  width: 820px;
  margin: 3% auto;
  .choose-room ::v-deep .el-select-dropdown {
    width: 168px;
  }
  ::v-deep .el-dialog__body {
    padding: 16px;
    overflow: inherit;
  }
}
::v-deep .el-checkbox {
  margin-right: 15px;
}
.class-time__wrap {
  display: flex;
  flex-direction: row;
}
</style>
