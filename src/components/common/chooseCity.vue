<template>
  <div>
    <el-cascader
      :options="options"
      :props="{
        expandTrigger: 'hover',
        value: `${remoteIsLabel ? 'value' : 'code'}`,
        label: 'value'
      }"
      v-model="selected"
      @change="handleChange"
      :placeholder="placeholder"
      :clearable="clearable"
      :disabled="disabled"
    />
  </div>
</template>

<script>
import commonApi from "@/api/common";
export default {
  name: "ChooseCity",
  props: {
    value: {
      type: Array,
      default: () => []
    },
    // 后台是否需要label方式接收
    remoteIsLabel: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: "请选择城市"
    },
    clearable: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selected: [],
      options: []
    };
  },
  watch: {
    value: {
      immediate: true,
      handler(val) {
        this.selected = val || [];
        // this.selected = val ? val.split("/") : [];
      }
    }
  },
  mounted() {
    this.getCityList();
  },
  methods: {
    handleChange(value) {
      console.log("value :>> ", value);
      this.$emit("input", value);
    },
    getCityList() {
      commonApi.getCityList().then((res) => {
        if (+res.status === 200) {
          this.options = res.data;
        }
      });
    }
  }
};
</script>
<style scoped></style>
