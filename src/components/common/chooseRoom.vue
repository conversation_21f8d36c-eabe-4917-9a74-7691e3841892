<!--选择教室-->
<template>
  <el-dialog
    :visible="true"
    title="选择教室"
    width="1000px"
    :before-close="handleClose"
    class="choose-room-package"
    :modal="has_modal"
    :append-to-body="true"
  >
    <div class="tg-dialog__content">
      <div class="class-list">
        <div class="search tg-box--margin">
          <el-form @submit.native.prevent :inline="true" :model="formInline">
            <el-form-item>
              <el-input
                placeholder="请输入教室名称"
                class="search__input"
                :class="{ 'search__input--special': !has_attribute }"
                v-model="formInline.course_name"
              >
                <img
                  src="../../assets/图片/icon_search_grey.png"
                  alt
                  slot="prefix"
                />
                <span @click="flag = !flag" slot="suffix" class="searchBtn">
                  <img
                    :src="
                      !flag
                        ? require('../../assets/图片/icon_double_down.png')
                        : require('../../assets/图片/icon_double_up_ac.png')
                    "
                    alt
                    class="search__img"
                  />
                </span>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                class="tg-button--primary tg-button__icon"
                @click="query"
              >
                <img
                  src="../../assets/图片/icon_search.png"
                  alt
                  class="tg-button__icon--normal"
                />查询
              </el-button>
              <el-button
                type="primary"
                class="tg-button--primary tg-button__icon"
                @click="resetForm"
              >
                <img
                  src="../../assets/图片/icon_reset.png"
                  alt
                  class="tg-button__icon--normal"
                />重置
              </el-button>
            </el-form-item>
            <el-form-item
              label="状态"
              class="tg-form-item tg-box--margin"
              v-if="flag"
            >
              <el-select
                placeholder="请选择状态"
                :popper-append-to-body="false"
                v-model="formInline.is_enabled"
              >
                <el-option
                  v-for="(item, index) in statusOptions"
                  :value="item.id"
                  :label="item.name"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="所属校区"
              class="tg-form-item tg-box--margin"
              v-if="flag"
            >
              <el-input
                placeholder="请选择所属校区"
                readonly
                show-word-limit
                :validate-event="false"
                @click.native="school_tree_visible = true"
                v-model="formInline.department_name"
                class="tg-select tg-select--dialog"
                @mouseenter.native="school_flag = true"
                @mouseleave.native="school_flag = false"
              >
                <img
                  slot="suffix"
                  style="height: 5px"
                  :src="
                    !school_flag
                      ? require('../../assets/图片/icon_more.png')
                      : require('../../assets/图片/icon_more_ac.png')
                  "
                  alt
                  class="btn__img--dotted"
                />
              </el-input>
              <school-tree
                :flag.sync="school_tree_visible"
                v-if="school_tree_visible"
                :name.sync="formInline.department_name"
                :id.sync="formInline.department_id"
                type="chooseSchool"
                :use_store_options="true"
              ></school-tree>
            </el-form-item>
          </el-form>
        </div>
        <div class="tg-table__box">
          <div class="tg-box--border"></div>
          <el-table
            ref="table"
            :data="list.results"
            tooltip-effect="dark"
            class="tg-table"
            :height="flag ? 353 : 449"
            @selection-change="handleSelectionChange"
            @current-change="handleCurrentChange"
            :row-key="getRowKeys"
            :row-style="rowStyle"
            :highlight-current-row="type == 'radio' ? true : false"
            @row-click="rowClick"
          >
            <el-table-column
              v-if="type != 'radio'"
              type="selection"
              width="50"
              :selectable="checkSelectable"
              :reserve-selection="true"
              label-class-name="cell_xx_hide"
            ></el-table-column>
            <el-table-column
              width="20"
              v-if="type == 'radio'"
            ></el-table-column>
            <el-table-column
              v-for="(item, index) in tableColumn"
              :key="index"
              :prop="item.prop"
              :label="item.label"
              :width="item.width"
              :show-overflow-tooltip="item.show"
            >
              <template slot-scope="scope">
                <span v-if="item.prop == 'is_enabled'">
                  {{ scope.row.is_enabled ? "启用" : "停用" }}
                </span>
                <span v-else>
                  {{ scope.row[scope.column.property] }}
                </span>
              </template>
            </el-table-column>
          </el-table>
          <div class="tg-pagination">
            <span class="el-pagination__total">共 {{ list.count }} 条</span>
            <el-pagination
              background
              layout="prev, pager, next,jumper"
              :total="list.count"
              :page-size="page_size"
              :current-page="page"
              @current-change="currentChange"
            >
            </el-pagination>
          </div>
        </div>
      </div>
      <div class="class-list--right">
        <div class="organization__title">
          <span
            >已选 教室<em>{{ right_room_list.length }}</em
            >个</span
          >
          <span class="all-clear" @click="clear">
            <img src="../../assets/图片/icon_clear.png" alt="" />
            清空
          </span>
        </div>
        <div
          class="organization__info"
          v-for="(item, index) in right_room_list"
          :key="index"
        >
          <span>{{ item.name }}</span>
          <img
            src="../../assets/图片/icon_close_green.png"
            alt=""
            @click="delOne(index, item.id)"
          />
        </div>
        <span v-if="right_room_list.length === 0" class="is-empty"
          >暂无数据</span
        >
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button class="tg-button--plain" type="plain" @click="back"
        >取消</el-button
      >
      <el-button class="tg-button--primary" type="primary" @click="really"
        >确定</el-button
      >
    </span>
  </el-dialog>
</template>
<script>
export default {
  name: "chooseRoom",
  data() {
    return {
      right_room_list: [],
      flag: false,
      page: 1,
      page_size: 10,
      total: 0,
      list: {},
      formInline: {
        name: "",
        is_enabled: "",
        department_name: "",
        department_id: ""
      },
      statusOptions: [
        { id: "", name: "不限" },
        { id: "true", name: "启用" },
        { id: "false", name: "停用" }
      ],
      tableColumn: [
        {
          prop: "index",
          label: "序号",
          show: false
        },
        {
          prop: "name",
          label: "教室名称",
          show: true
        },
        {
          prop: "department_name",
          label: "所属校区",
          show: false
        },
        {
          prop: "max_student_numb",
          label: "容纳人数",
          show: false
        },
        {
          prop: "acreage",
          label: "教室面积(㎡)",
          width: "120",
          show: false
        },
        {
          prop: "memo",
          label: "教室备注",
          show: true
        },
        {
          prop: "is_enabled",
          label: "教室状态",
          show: false
        }
      ],
      school_tree_visible: false,
      school_flag: false,
      has_attribute: false,
      loading: false,
      search_flag: false,
      rowSelectFlag: false,
      categroy_list: []
    };
  },
  props: {
    check_id: String,
    check_name: String,
    check_arr: {
      type: Array,
      default: () => []
    },
    has_modal: {
      type: Boolean,
      default: true
    },
    type: {
      type: String,
      default: "radio"
    },
    hide_header_checkbox: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    // 获取教室列表
    schoolroom_list() {
      return this.$store.getters.doneGetSchoolroomList;
    },
    schoolIds() {
      return this.$store.getters.doneGetSchoolId;
    }
  },
  watch: {
    schoolroom_list(new_obj) {
      console.log(new_obj);
      this.loading = false;
      this.list = new_obj;
    }
  },
  mounted() {
    // 暂时隐藏全选，后续需要后台做处理
    if (this.hide_header_checkbox) {
      this.$nextTick(() => {
        // eslint-disable-next-line no-undef
        $(".choose-room-package .cell_xx_hide.cell").hide();
      });
    }
    this.getlist();
    this.right_room_list = JSON.parse(JSON.stringify(this.check_arr)) || [];
  },
  methods: {
    // 查询
    query() {
      this.page = 1;
      this.getlist();
    },
    // 重置
    resetForm() {
      this.formInline = {
        name: "",
        is_enabled: ""
      };
      this.page = 1;
      this.pageSize = 10;
      this.getlist();
    },
    getlist(page) {
      const { department_id } = this.formInline;
      this.list.results = [];
      this.loading = true;
      this.$store.dispatch("getSchoolroomList", {
        page: page || 1,
        page_size: this.pageSize,
        ...this.formInline,
        department_id: department_id || this.schoolIds
      });
    },
    rowStyle({ row }) {
      if (row.is_sell === 2) {
        return { color: "#C0CCDA", cursor: "not-allowed" };
      }
    },
    checkSelectable(row) {
      return row.is_sell === 1;
    },
    handleClose() {
      this.$emit("close");
    },
    delOne(index) {
      if (this.type === "radio") {
        this.$nextTick(() => {
          this.$refs.table.setCurrentRow();
        });
        this.right_room_list.splice(index, 1);
      } else {
        setTimeout(() => {
          this.rowSelectFlag = true;
          const id = this.right_room_list[index].id;
          this.list.forEach((item) => {
            if (item.id === id) {
              this.$refs.table.toggleRowSelection(item, false);
            }
          });
          this.right_room_list.splice(index, 1);
          this.rowSelectFlag = false;
        }, 0);
      }
    },
    clear() {
      if (this.type === "radio") {
        this.$nextTick(() => {
          this.$refs.table.setCurrentRow();
        });
      } else {
        this.$nextTick(() => {
          this.$refs.table.clearSelection();
          this.right_room_list = [];
        });
      }
    },
    back() {
      this.$emit("close");
    },
    really() {
      const ids = [];
      const names = [];
      this.right_room_list.forEach((item) => {
        ids.push(item.id);
        names.push(item.name);
      });
      this.$emit(
        "update:check_id",
        this.right_room_list.length > 0 ? ids.toString() : ""
      );
      this.$emit(
        "update:check_name",
        this.right_room_list.length > 0 ? names.toString() : ""
      );
      this.$emit(
        "update:check_arr",
        this.right_room_list.length > 0 ? this.right_room_list : []
      );
      console.log(ids, names);
      this.$emit(
        "confirm",
        this.right_room_list.length > 0 ? this.right_room_list : []
      );
      this.$emit("close");
    },
    currentChange(val) {
      this.page = val;
      this.getlist(val);
    },
    getRowKeys(row) {
      // this.$refs.formInline.clearSelection();完成后需要手动清空
      return row.id;
    },
    handleSelectionChange(val) {
      if (this.type !== "radio") {
        if (this.rowSelectFlag) return;
        const ids = this.right_room_list.map((item) => item.id);
        const arr = JSON.parse(JSON.stringify(this.right_room_list));
        const new_arr = val.map((item) => {
          if (ids.indexOf(item.id) !== -1) {
            item = arr[ids.indexOf(item.id)];
          }
          return item;
        });
        this.right_room_list = new_arr;
      }
    },

    handleCurrentChange(val) {
      if (this.type === "radio") {
        this.right_room_list = val == null ? [] : [val];
      }
    }
  },
  created() {
    this.right_room_list = JSON.parse(JSON.stringify(this.check_arr)) || [];
  }
};
</script>
<style lang="less" scoped>
.choose-room-package {
  .search {
    width: 100%;

    img {
      width: 14px;
      height: 14px;
      margin-right: 12px;
      margin-left: 11px;
      margin-top: -4px;
      vertical-align: middle;
    }

    img.search__img {
      width: 8px;
      height: 12px;
      margin-right: 10px;
      cursor: pointer;
    }
    .searchBtn {
      display: inline-block;
      width: 100%;
      height: 100%;
      cursor: pointer;
      user-select: none;
    }
    ::v-deep .el-form-item__content,
    ::v-deep .el-form-item__label {
      line-height: 32px;
    }

    .search__input {
      width: 280px;

      ::v-deep .el-input__inner {
        padding-left: 40px;
      }

      ::v-deep .el-input__suffix {
        right: 1px;
        background: #ebf4ff;
        height: 30px;
        top: 1px;
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
      }
    }

    .search__input--special {
      width: 413px;
    }

    .el-button {
      width: 72px;

      img {
        margin-left: 0;
        margin-right: 7px;
      }
    }

    .search-status {
      ::v-deep .el-input {
        width: 120px;

        .el-input__inner {
          padding-left: 16px;
        }
      }
    }

    ::v-deep .el-form-item {
      margin-right: 10px;
    }

    ::v-deep .el-form-item:nth-child(2) {
      margin-right: 0;
    }

    ::v-deep .el-form-item.search-status {
      margin-right: 10px;
    }

    ::v-deep .tg-form-item--right {
      margin-right: 20px;
    }

    ::v-deep .tg-form-item .el-input {
      width: 225px;
    }
  }
  ::v-deep .el-dialog__body {
    padding: 0;
  }
  .tg-dialog__content {
    display: flex;
    flex-direction: row;
    height: 589px;
  }
  .class-list {
    width: calc(100% - 274px);
    border-right: 1px solid #e0e6ed;
    padding: 16px;
    .tg-table__box {
      margin-left: 0;
      margin-right: 0;
    }
    ::v-deep .el-table {
      padding: 0;
      th {
        background: #f5f8fc;
      }
      .el-table__header {
        padding: 0;
        background: #f5f8fc;
      }
      .el-table__body {
        padding: 0;
      }
      // .el-table__row {
      //   td {
      //     margin-right: 5px;
      //   }
      // }
    }
  }
  .class-list--right {
    width: 257px;
    margin-left: 16px;
    margin-right: 16px;
    margin-top: 16px;
    height: 414px;
    overflow: auto;
    .organization__title,
    .organization__info {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }
    .all-clear {
      // color: #157df0;
      font-size: @text-size_small;
      font-family: @text-famliy_medium;
      cursor: pointer;
      img {
        width: 14px;
        height: 14px;
        margin-right: 8px;
        vertical-align: middle;
        margin-top: -3px;
      }
    }
    .organization__title {
      em {
        font-style: normal;
        color: @base-color;
      }
    }
    .organization__info {
      border: 1px solid @base-color;
      border-radius: 4px;
      height: 40px;
      align-items: center;
      padding: 0 16px;
      margin-top: 16px;
      ::v-deep .el-input,
      ::v-deep .el-input__inner {
        height: 40px;
        line-height: 40px;
      }
      img {
        width: 16px;
        height: 16px;
        cursor: pointer;
      }
      span:nth-child(1) {
        overflow-x: scroll;
        width: calc(100% - 36px);
        white-space: nowrap;
      }
    }
    .required {
      &::before {
        content: "*";
        margin-right: 5px;
        color: #ff0317;
      }
    }
  }
  .is-empty {
    color: @text-color_third;
    width: 100%;
    display: inline-block;
    text-align: center;
    line-height: 350px;
  }
  .tg-select {
    width: 100%;
  }
  .choose-categroy {
    width: calc(100% - 468px - 88px);
    margin-right: 0;
    ::v-deep .el-input {
      width: 100%;
    }
    ::v-deep .el-select-dropdown {
      width: 240px;
    }
  }
}
</style>
