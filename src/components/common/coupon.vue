<template>
  <el-dialog
    :visible="true"
    title="优惠券"
    width="800px"
    :before-close="close"
    class="coupon-class"
  >
    <div v-loading="loading" class="tg-dialog__content clearfix">
      <template>
        <div class="coupon-box" v-for="item in list" :key="item.id">
          <!-- 直减券 -->
          <div
            v-if="item.coupon_type === 1"
            @click="chooseOne(item)"
            :class="{ disabled: item.disabled }"
            class="coupon manjian"
          >
            <div class="amount-num"><em>￥</em>{{ item.quota / 100 }}</div>
            <div class="describe">
              <div class="name">
                {{ item.name
                }}<span class="can-overlay" v-if="item.is_overlay">可叠加</span>
              </div>
              <div class="date">
                有效期至：{{
                  moment(item.valid_time_start).format("YYYY.MM.DD")
                }}-{{ moment(item.valid_time_end).format("YYYY.MM.DD") }}
              </div>
              <div class="use-ctrl">
                <span :class="{ on: item.checked }" class="coupon-check"></span>
                <span class="text">{{
                  item.disabled ? "不可用" : "立即使用"
                }}</span>
              </div>
            </div>
          </div>
          <!-- 折扣券 -->
          <div
            v-else-if="item.coupon_type === 2"
            @click="chooseOne(item)"
            class="coupon zhekou"
            :class="{ disabled: item.disabled }"
          >
            <div class="amount-num">{{ item.quota / 10 }}<em>折</em></div>
            <div class="describe">
              <div class="name">
                {{ item.name }}
              </div>
              <div class="date">
                有效期至：{{
                  moment(item.valid_time_start).format("YYYY.MM.DD")
                }}-{{ moment(item.valid_time_end).format("YYYY.MM.DD") }}
              </div>
              <div class="use-ctrl">
                <span :class="{ on: item.checked }" class="coupon-check"></span>
                <span class="text">{{
                  item.disabled ? "不可用" : "立即使用"
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </template>
      <div v-if="!list.length" class="tg_no-coupon">暂无可用优惠券</div>
    </div>
    <span slot="footer" class="dialog-cls-footer">
      <div class="check-list">
        已选择优惠券
        <span style="color: #2d80ed">{{ checked_list.length }}</span> 张
      </div>
      <div>
        <el-button class="tg-button--plain" type="plain" @click="close"
          >取消</el-button
        >
        <el-button class="tg-button--primary" type="primary" @click="confirm"
          >确定</el-button
        >
      </div>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: "Coupon",
  props: {
    chooseIds: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      required: true,
      default: () => []
    }
  },
  data() {
    return {};
  },
  watch: {},
  computed: {
    // 需要记录先后顺序
    checked_list() {
      const arr = [];
      this.chooseIds.map((val) => {
        const obj = this.list.find((item) => item.id === val);
        arr.push(obj);
      });
      return arr;
    }
  },
  created() {
    this.$emit("init");
  },
  methods: {
    initChoose() {
      const { chooseIds } = this;
      this.list.map((item) => {
        if (chooseIds.indexOf(item.id) > -1) {
          this.$set(item, "checked", true);
        }
      });
    },
    close() {
      this.$emit("close");
    },
    confirm() {
      this.$emit("update:chooseIds", this.chooseIds);
      this.$emit("confirm", this.checked_list);
    },
    chooseOne(item) {
      if (item.disabled) {
        return;
      }
      // 需要记录先后顺序
      const { checked } = item;
      if (!checked) {
        this.chooseIds.push(item.id);
      } else {
        this.chooseIds.map((val, index) => {
          if (item.id === val) {
            this.chooseIds.splice(index, 1);
          }
        });
      }
      this.$set(item, "checked", !checked);
      this.$emit("chooseOne", item);
    }
  }
};
</script>

<style lang="less" scoped>
.coupon-class {
  ::v-deep .el-dialog__body {
    padding: 0;
    overflow: auto;
    height: 355px;
  }

  .tg-dialog__content {
    padding: 16px;
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    height: 100%;
    .tg_no-coupon {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #bdbdbd;
      height: 355px;
      flex: 1;
    }
    .coupon-box {
      margin-right: 16px;
      margin-bottom: 16px;
      &:nth-child(2n) {
        margin-right: 0px;
      }
    }
    .coupon {
      display: flex;
      align-items: center;
      width: 374px;
      height: 120px;
      box-sizing: border-box;
      cursor: pointer;
      &.disabled {
        cursor: not-allowed;
        opacity: 0.5;
      }
      .amount-num {
        display: block;
        width: 120px;
        height: 120px;
        line-height: 120px;
        text-align: center;
        color: #fff;
        font-weight: bold;
        font-size: 40px;
        background-size: cover;
        text-shadow: 2px 2px 1px #24d1a1;
        em {
          font-style: normal;
          font-size: 24px;
          font-weight: bold;
          vertical-align: middle;
        }
      }

      .describe {
        position: relative;
        width: 256px;
        height: 120px;
        padding: 24px 16px;
        box-sizing: border-box;
        background-color: #fff;
        background-size: cover;

        .name {
          font-size: 14px;
          color: #475669;
          box-sizing: border-box;
          margin-bottom: 8px;
          display: flex;
          align-items: center;
          font-weight: 500;

          span {
            border: 1px solid #fd6865;
            background-color: #ffeae9;
            width: 54px;
            height: 20px;
            line-height: 20px;
            border-radius: 4px;
            display: inline-block;
            margin-left: 16px;
            font-size: 10px;
            text-align: center;
            color: #fd6865;
          }
        }

        .date {
          color: #475669;
          font-size: 12px;
          margin-bottom: 12px;
        }
        .use-ctrl {
          display: flex;
          align-items: center;
          .text {
            font-size: 12px;
            color: #9e5d2f;
            margin-left: 6px;
          }
        }
        .coupon-check {
          display: block;
          width: 18px;
          height: 18px;
          background-size: cover;
          opacity: 0.4;
          transition: all 0.3s;
          &.on {
            opacity: 1;
          }
        }
      }

      &.manjian {
        .amount-num {
          color: #753800;
          text-shadow: 2px 2px 1px #ebc33d;
          background-image: url("~@/assets/图片/coupon-bg1.png");
          em {
            margin-right: 3px;
          }
        }

        .describe {
          background-image: url("~@/assets/图片/coupon-bg1-1.png");
        }
        .name {
          .can-overlay {
            width: 50px;
            height: 20px;
            line-height: 20px;
            font-size: 12px;
            color: #9e5d2f;
            border: 1px solid #eabc51;
            background-color: #fffdf1;
            text-align: center;
            display: block;
          }
        }
        .coupon-check {
          background-image: url("~@/assets/图片/icon_circle_right.png");
          &.on {
            background-image: url("~@/assets/图片/icon_circle_right_ac.png");
          }
        }
      }

      &.zhekou {
        .amount-num {
          background-image: url("~@/assets/图片/coupon-bg2.png");
          color: #fff;
          em {
            margin-left: 3px;
          }
        }
        .use-ctrl {
          .text {
            font-size: 12px;
            color: #2d80ed;
            margin-left: 6px;
          }
        }
        .describe {
          background-image: url("~@/assets/图片/coupon-bg2-1.png");
        }

        .coupon-check {
          background-image: url("~@/assets/图片/icon_circle_right_2.png");
          &.on {
            background-image: url("~@/assets/图片/icon_circle_right_ac.png");
          }
        }
      }

      &:nth-child(2n) {
        margin-right: 0;
      }
    }
  }
}
</style>
