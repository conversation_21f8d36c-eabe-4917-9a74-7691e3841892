<template>
  <el-popover :placement="placement" :width="width" :trigger="trigger">
    <div class="tg-table__box tg-box--margin tg-table-container">
      <div class="tg-box--border"></div>
      <el-table
        :data="tableData"
        border
        ref="goodsTable"
        show-overflow-tooltip
        :span-method="objectSpanMethod"
        show-summary
        :summary-method="getSummaries"
      >
        <template v-for="item in table_title">
          <el-table-column
            v-if="item.show"
            :key="item.prop"
            :prop="item.prop"
            :label="item.label"
            :width="item.width"
          >
            <template slot-scope="{ row }">
              <template v-if="item.prop === 'article_name'">
                <span v-if="source === 'teachAidPackage'">
                  {{ row.name }}
                </span>
                <span v-else>
                  {{ row.article_name }}
                </span>
              </template>
              <template v-else>
                {{ row[item.prop] }}
              </template>
            </template>
          </el-table-column>
        </template>
      </el-table>
    </div>
    <el-button @click="showTable" type="text" slot="reference">
      {{ goodsNum }}
    </el-button>
  </el-popover>
</template>

<script>
import teachingAidPackageManagement from "@/api/teachingAidPackageManagement.js";
export default {
  name: "tablePopover",
  props: {
    placement: {
      type: String,
      default: "right"
    },
    source: {
      type: String,
      default: ""
    },
    width: {
      type: String,
      default: "400"
    },
    trigger: {
      type: String,
      default: "click"
    },
    goodsNum: {
      type: [String, Number],
      default: 0
    },
    goodsId: {
      type: [String, Number],
      default: 0
    },
    // 是否是收费相关页面用
    is_charge: {
      type: Boolean,
      default: false
    },
    department_id: {
      type: [String, Array],
      default: ""
    },
    goods: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      tableData: [],
      table_title: [
        {
          label: "物品名称",
          prop: "article_name",
          show: true
        },
        {
          label: "物品代码",
          prop: "article_code",
          width: "120",
          show: true
        },
        {
          label: "采购单价",
          prop: "purchase_price",
          show: !this.is_charge
        },
        {
          label: "销售单价",
          prop: "sell_price",
          show: !this.is_charge
        },
        {
          label: "教辅包销售单价",
          prop: "article_sell_price",
          width: "120",
          show: true
        },
        {
          label: "包含物品数量",
          prop: "number",
          width: "120",
          show: true
        },
        {
          label: "仓库剩余数量",
          prop: "current_number",
          width: "120",
          show: this.source !== "teachAidPackage" && !this.is_charge
        }
      ]
    };
  },
  methods: {
    getList() {
      const params = {
        id: this.goodsId,
        department_id: this.department_id
      };
      teachingAidPackageManagement
        .teachAidPackageArticleInfo(params)
        .then((res) => {
          const { code, data } = res;
          if (code === 0) {
            console.log(this.goods.length);
            if (this.goods.length) {
              console.log(this.goods);
              console.log(data);
              const curGoodList = this.goods.map((i) => {
                const isIncludesGoodId = data.find(
                  (j) => j.article_id === i.good_id
                );
                if (isIncludesGoodId) {
                  i.article_name = isIncludesGoodId.article_name;
                  i.article_code = isIncludesGoodId.article_code;
                } else {
                  i.article_name = i.good_name;
                }
                i.article_sell_price = i.purchased_single_price;
                i.number = i.number_left;
                return i;
              });
              console.log(curGoodList);
              this.tableData = curGoodList.map((i) => ({
                ...i,
                total: i.article_sell_price * i.number
              }));
            } else {
              console.log(data);
              this.tableData = data.map((i) => ({
                ...i,
                total: i.article_sell_price2 * i.number
              }));
            }
          }
        });
    },
    getList2() {
      const params = { id: this.goodsId };
      teachingAidPackageManagement.teachAidPackageInfo(params).then((res) => {
        this.tableData = res.data.article_list.map((i) => ({
          ...i,
          total: i.article_sell_price * i.number
        }));
      });
      console.log(this.tableData);
    },
    showTable() {
      console.log(this.source);
      if (this.source === "teachAidPackage") {
        this.getList2();
      } else {
        this.getList();
      }
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "合计";
          return;
        }
        const values = data.map((item) =>
          Number(
            this.goods.length
              ? item.total
              : this.source === "teachAidPackage"
              ? item.total
              : item.total_sell_price
          )
        );
        console.log(data);
        if (columns[index].property === "article_sell_price") {
          sums[1] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          sums[1] = sums[1].toFixed(2) + "元";
        }
      });
      console.log("sums :>> ", sums);
      return sums;
    },
    objectSpanMethod() {
      this.$nextTick(() => {
        if (this.$refs.goodsTable.$el) {
          const current = this.$refs.goodsTable.$el
            .querySelector(".el-table__footer-wrapper")
            .querySelector(".el-table__footer");
          const cell = current.rows[0].cells;
          if (this.is_charge) {
            this.mergeCell(cell, 0, 2);
          } else {
            this.mergeCell(cell, 0, 4);
          }
          this.mergeCell(cell, 1, 2);
        }
      });
    },
    mergeCell(cell, mergeStar, mergeNum) {
      cell[mergeStar].colSpan = mergeNum; // 合并单元格
      cell[mergeStar].style.textAlign = "center"; // 合计行第一列字段居中显示。
      if (this.is_charge) {
        cell[mergeStar + mergeNum].style.display = "none";
      } else {
        cell[mergeStar + mergeNum - 1].style.display = "none";
      }
    }
  }
};
</script>

<style>
.width {
  width: 200px;
}
</style>
