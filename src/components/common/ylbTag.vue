<template>
  <div>
    <!-- <span v-if="ylb_status === 1" class="ylb-tag-text ylb-tag-text--gray"
      >否</span
    > -->
    <span v-if="ylb_status === 2" class="ylb-tag-text ylb-tag-text--blue"
      >元</span
    >
    <span v-else-if="ylb_status === 3" class="ylb-tag-text ylb-tag-text--gray"
      >元</span
    >
  </div>
</template>

<script>
export default {
  name: "YlbTag",
  props: {
    // ylb_status   1 未购买过  2 已购买且持有 3  已购买并退费
    ylb_status: {
      type: Number,
      default: 1
    }
  }
};
</script>

<style lang="less" scoped>
.ylb-tag-text {
  color: #fff;
  border-radius: 100%;
  border: 3px solid #e6eaec;
  display: block;
  width: 30px;
  height: 30px;
  line-height: 26px;
  text-align: center;
  font-size: 12px;
  &--gray {
    background-color: #999;
  }
  &--blue {
    background-color: #0099ff;
    border: 3px solid #c4e2f2;
  }
}
</style>
