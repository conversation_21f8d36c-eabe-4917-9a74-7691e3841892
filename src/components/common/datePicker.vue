<template>
  <div class="date-picker-wrap">
    <el-select
      v-model="value"
      @change="handleFastDateSelect"
      placeholder="请选择"
    >
      <el-option
        v-for="item in fastDatePicker"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      >
      </el-option>
    </el-select>
    <div class="date-picker">
      <el-date-picker
        v-model="startTime"
        type="date"
        @change="(val) => handlePickerChage(val, 'start')"
        placeholder="开始日期"
        :picker-options="startDatepickerOptions"
        value-format="yyyy-MM-dd"
      >
      </el-date-picker>
      <span class="separate">至</span>
      <el-date-picker
        v-model="endTime"
        type="date"
        @change="(val) => handlePickerChage(val, 'end')"
        placeholder="结束日期"
        :picker-options="endDatepickerOptions"
        value-format="yyyy-MM-dd"
      >
      </el-date-picker>
    </div>
  </div>
</template>

<script>
export default {
  name: "datePicker",
  props: {
    pickerDate: {
      type: [Array, String],
      default: () => [null, null]
    },
    fastDatePicker: {
      type: Array,
      default: () => [
        {
          label: "昨日",
          value: -1
        },
        {
          label: "今日",
          value: 0
        },
        {
          label: "近一周",
          value: 7
        },
        {
          label: "本月",
          value: -30
        },
        {
          label: "近一个月",
          value: 30
        }
      ]
    }
  },
  model: {
    prop: "pickerDate",
    event: "pickerChange"
  },
  watch: {
    pickerDate: {
      immediate: true,
      handler(val) {
        console.log(val);
        if (val === "") {
          this.startTime = null;
          this.endTime = null;
          this.value = "";
        } else {
          console.log(
            this.moment(val[0]).format("YYYY-MM-DD"),
            this.moment(val[1]).format("YYYY-MM-DD")
          );
          this.startTime = val[0]
            ? this.moment(val[0]).format("YYYY-MM-DD")
            : undefined;
          this.endTime = val[1]
            ? this.moment(val[1]).format("YYYY-MM-DD")
            : undefined;
        }
      }
    }
  },
  data() {
    return {
      value: 7,
      pickerTimes: [],
      startTime: null,
      endTime: null,
      startDatepickerOptions: {
        disabledDate: (time) => {
          return this.endTime
            ? new Date(this.moment(time).format("YYYY-MM-DD")).getTime() >
                new Date(this.endTime).getTime()
            : false;
        }
      },
      endDatepickerOptions: {
        disabledDate: (time) => {
          return this.startTime
            ? new Date(this.moment(time).format("YYYY-MM-DD")).getTime() <
                new Date(this.startTime).getTime()
            : false;
        }
      }
    };
  },
  methods: {
    formattedYesterday() {
      // 获取当前日期和时间
      const today = new Date();

      // 计算昨天的日期
      const yesterday = new Date(today);
      yesterday.setDate(today.getDate() - 1);

      // 格式化昨天的日期（可选）
      const year = yesterday.getFullYear();
      const month = String(yesterday.getMonth() + 1).padStart(2, "0"); // 月份从0开始，所以要加1，并补零
      const day = String(yesterday.getDate()).padStart(2, "0"); // 补零

      return `${year}-${month}-${day}`;
    },
    handleFastDateSelect() {
      if (this.value === -1) {
        this.startTime = this.formattedYesterday();
        this.endTime = this.formattedYesterday();
      } else if (this.value === -30) {
        this.startTime = this.moment().startOf("month").format("YYYY-MM-DD");
        this.endTime = this.moment().endOf("month").format("YYYY-MM-DD");
      } else {
        const start = new Date();
        const end = new Date();
        this.startTime = this.moment(
          start.getTime() - 3600 * 1000 * 24 * this.value
        ).format("YYYY-MM-DD");
        this.endTime = this.moment(end).format("YYYY-MM-DD");
      }
      this.updatePickerTime();
    },
    handlePickerChage(val, type) {
      if (type === "start") {
        this.pickerTimes[0] = val;
        this.startTime = val;
      } else {
        this.pickerTimes[1] = val;
        this.endTime = val;
      }
      this.updatePickerTime();
    },
    updatePickerTime() {
      // const defaultTime = [1000 * 60 * 60 * 4, 1000 * 60 * 60 * 4];
      // const defaultTime = [0, 0];
      // const start = new Date(this.startTime + defaultTime[0]);
      // const end = new Date(this.endTime + defaultTime[1]);
      console.log(this.startTime, this.endTime);
      this.$emit("pickerChange", [this.startTime, this.endTime]);
      // this.$emit("pickerChange", [
      //   this.startTime ? moment(Number(this.startTime)).format() : "",
      //   this.endTime ? moment(Number(this.endTime)).format() : ""
      // ]);
    }
  }
};
</script>

<style scoped lang="less">
.date-picker-wrap {
  display: flex;
  /deep/ .el-input {
    width: 100px;
    .el-input__inner {
      height: 34px;
      border-right: none;
      line-height: 34px;
    }
  }
  /deep/ .date-picker {
    background: #fff;
    border-radius: 4px;
    border: 1px solid #e0e6ed;
    box-sizing: border-box;
    height: 34px;
    .el-date-editor {
      width: 150px;
    }
    .el-input__inner {
      background: none;
      border: none;
    }
    .separate {
      padding: 0 2px;
      color: #1f2d3d;
      font-size: 14px;
      display: inline-block;
    }
  }
}
</style>
