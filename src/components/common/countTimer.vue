<template>
  <span v-if="time">
    <slot
      v-bind="{
        d: days,
        h: hours,
        m: mins,
        s: seconds,
        hh: `00${hours}`.slice(-2),
        mm: `00${mins}`.slice(-2),
        ss: `00${seconds}`.slice(-2)
      }"
    ></slot>
  </span>
</template>

<script>
export default {
  name: "CountTimer",
  props: {
    // 传入时间戳--毫秒
    time: {
      type: [String, Number],
      default: 0
    },
    refreshCounter: {
      type: [String, Number],
      default: 0
    }
  },

  computed: {
    duration() {
      // 处理入剩余时间
      return Math.round(+this.time / 1000);
    }
  },

  data() {
    return {
      days: "0",
      hours: "00",
      mins: "00",
      seconds: "00",
      timer: null,
      curTime: 0 // 当前的时刻，也就是显示在页面上的那个时刻
    };
  },

  methods: {
    // 将duration转化成天数，小时，分钟，秒数的方法
    durationFormatter(time) {
      if (!time) return { ss: 0 };
      let t = time;
      const ss = t % 60;
      t = (t - ss) / 60;
      if (t < 1) return { ss };
      const mm = t % 60;
      t = (t - mm) / 60;
      if (t < 1) return { mm, ss };
      const hh = t % 24;
      t = (t - hh) / 24;
      if (t < 1) return { hh, mm, ss };
      const dd = t;
      // console.log(dd, hh, mm, ss);
      return { dd, hh, mm, ss };
    },
    // 开始执行倒计时的方法
    countDown() {
      // 记录下当前时间
      this.curTime = Date.now();
      this.getTime(this.duration);
    },
    // 倒计时方法
    getTime(time) {
      this.timer && clearTimeout(this.timer);
      if (time < 0) {
        this.$emit("ended");
        return;
      }
      const { dd, hh, mm, ss } = this.durationFormatter(time);
      this.days = dd || 0;
      this.hours = hh || 0;
      this.mins = mm || 0;
      this.seconds = ss || 0;
      this.timer = setTimeout(() => {
        const now = Date.now();
        const diffTime = Math.floor((now - this.curTime) / 1000);
        const step = diffTime > 1 ? diffTime : 1;
        this.curTime = now;
        this.getTime(time - step);
      }, 1000);
    }
  },

  mounted() {
    this.countDown();
  },

  watch: {
    duration() {
      this.countDown();
    },
    refreshCounter() {
      this.countDown();
    }
  }
};
</script>
