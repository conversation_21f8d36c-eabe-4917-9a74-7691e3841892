<template>
  <div v-if="mobileTemInfo.mobile" class="copy_name">
    {{
      $_has({ m: "all_phone", o: "has_limit" })
        ? mobileTemInfo.mobile
        : mobileTemInfo.has_eye_limit
        ? mobileTemInfo.mobile
        : mobileTemInfo?.mobile?.replace(/^(\d{3})\d{4}(\d+)/, `$1****$2`)
    }}
    <div
      v-show="!$_has({ m: 'all_phone', o: 'has_limit' })"
      :class="{
        open_eye: mobileTemInfo.has_eye_limit,
        close_eye: !mobileTemInfo.has_eye_limit
      }"
      @click="
        mobileTemInfo.has_eye_limit
          ? $set(mobileTemInfo.row, 'has_eye_limit', false)
          : $set(mobileTemInfo.row, 'has_eye_limit', true)
      "
    ></div>
    <div class="weihu-custom">
      <template v-if="mobileTemInfo.isShowWeihu">
        <span v-if="$_has({ m: 'call_manage', o: 'extension_info' })">
          <i
            v-if="mobileTemInfo.row.isCalled"
            style="color: #1b9732; cursor: not-allowed"
            class="el-icon-phone-outline"
          ></i>
          <i
            v-else
            @click="createVoip(mobileTemInfo.row)"
            class="el-icon-phone"
          ></i
        ></span>
      </template>
      <template v-if="mobileTemInfo.isShowCallLog">
        <span>
          <i
            v-if="$_has({ m: 'call_manage', o: 'call_record' })"
            @click="openCallLog(mobileTemInfo.row)"
            class="el-icon-time"
          ></i
        ></span>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: "mobileHyposensitization",
  props: {
    mobileTemInfo: {
      type: Object,
      default: () => ({
        // 承载has_eye_limit、isCalled的object
        row: {
          has_eye_limit: false
        },
        // 是否显示weihu按钮
        isShowWeihu: false,
        // 是否显示通话记录
        isShowCallLog: false,
        // 是否默认脱敏手机号
        has_eye_limit: false,
        // 手机号
        mobile: ""
      })
    }
  },
  methods: {
    createVoip(row) {
      this.$emit("createVoip", row);
    },
    openCallLog(row) {
      this.$emit("openCallLog", row);
    }
  }
};
</script>

<style lang="less" scoped>
.weihu-custom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  span {
    display: flex;
    align-items: center;
    i {
      font-size: 20px;
      cursor: pointer;
      color: #2d80ed;
      margin: 0 6px;
    }
  }
}
</style>
