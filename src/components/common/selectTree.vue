<template>
  <el-select
    class="main-select-tree"
    ref="selectTree"
    v-model="value"
    clearable
    @clear="clearSelectInput"
  >
    <el-input
      placeholder="请输入"
      v-model="filterText"
      style="margin-bottom: 10px"
    ></el-input>
    <el-option
      v-for="item in formatData(treeData)"
      :key="item.value"
      :label="item.name"
      :value="item.value"
      style="display: none"
    />
    <el-tree
      class="main-select-el-tree"
      ref="selecteltree"
      :data="treeData"
      :props="defaultProps"
      node-key="id"
      highlight-current
      @node-click="handleNodeClick"
      :current-node-key="value"
      :expand-on-click-node="false"
      default-expand-all
      :filter-node-method="filterNode"
    >
    </el-tree>
  </el-select>
</template>

<script>
export default {
  name: "selectTree",
  props: {
    treeData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      filterText: "",
      value: "",
      defaultProps: {
        children: "children",
        label: "name"
      }
    };
  },
  watch: {
    filterText(val) {
      this.$refs.selecteltree.filter(val);
    }
  },
  created() {
    console.log(this.treeData);
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    formatData(data) {
      const options = [];
      const formatDataRecursive = (data) => {
        data.forEach((item) => {
          options.push({ label: item.name, value: item.id });
          if (item.children && item.children.length > 0) {
            formatDataRecursive(item.children);
          }
        });
      };
      formatDataRecursive(data);
      return options;
    },
    // 点击事件
    handleNodeClick(node) {
      if (node.level >= 2) {
        this.value = node.name;
        this.$emit("change", node);
        this.$refs.selectTree.blur();
      }
    },
    // 清空事件
    clearSelectInput() {
      // 获取 el-tree 实例的引用
      const elTree = this.$refs.selecteltree;
      // 将当前选中的节点设置为 null
      elTree.setCurrentKey(null);
    }
  }
};
</script>

<style>
.main-select-el-tree .el-tree-node .is-current > .el-tree-node__content {
  font-weight: bold;
  color: #409eff;
}
.main-select-el-tree .el-tree-node.is-current > .el-tree-node__content {
  font-weight: bold;
  color: #409eff;
}
</style>
