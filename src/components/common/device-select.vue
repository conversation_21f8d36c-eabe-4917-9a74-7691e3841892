<!--
 * @Description: 设备选择弹窗（摄像头，麦克风，扬声器）
 * props:
 *   deviceType: String 'microphone'|'speaker'|'camera'
 *   onChange: Function 监听select的 change 事件的执行函数
 *   disabled: 是否可选择，默认值为false
 * @Date: 2021-10-27 17:20:31
 * @LastEditTime: 2021-10-28 22:13:08
-->

<template>
  <el-select
    v-model="activeDeviceId"
    :disabled="disabled"
    :placeholder="placeholder"
    @change="handleChange"
    :popper-append-to-body="true"
  >
    <el-option
      v-for="item in deviceList"
      :key="item.deviceId"
      :label="item.label ?? item.deviceName"
      :value="item.deviceId"
    ></el-option>
  </el-select>
</template>

<script>
import TRTC from "trtc-sdk-v5";
// import TXLive from "../mixins/TXLive.js";

export default {
  name: "DeviceSelect",
  props: {
    deviceType: String,
    onChange: Function,
    disabled: {
      type: Boolean,
      default: false
    }
  },
  // mixins: [TXLive],
  data() {
    return {
      deviceList: [],
      activeDevice: {},
      activeDeviceId: "",
      showTXLivePush: false
    };
  },
  computed: {
    stageStudentNum: {
      get() {
        return parseInt(localStorage.getItem("live_studentTotal"));
      },
      set(v) {}
    },
    activeMicrophoneId() {
      return this.$store.getters.activeMicrophoneId;
    },
    activeSpeakerId() {
      return this.$store.getters.activeSpeakerId;
    },
    activeCameraId() {
      return this.$store.getters.activeCameraId;
    },
    placeholder() {
      const relation = {
        camera: "摄像头",
        microphone: "麦克风",
        speaker: "扬声器"
      };
      return "请选择" + relation[this.deviceType];
    }
  },
  watch: {
    deviceType: {
      handler(val) {
        console.log("deviceType", val);
        // if (this.stageStudentNum > 0) {
        this.getDeviceList();
        // }
      },
      deep: true,
      immediate: true
    },
    activeMicrophoneId: {
      immediate: true,
      handler(val) {
        console.log(val);
        // && this.stageStudentNum > 0
        if (this.deviceType === "microphone") {
          this.activeDeviceId = val;
        }
      }
    },
    activeSpeakerId: {
      immediate: true,
      handler(val) {
        // && this.stageStudentNum > 0
        if (this.deviceType === "speaker") {
          this.activeDeviceId = val;
        }
      }
    },
    activeCameraId: {
      immediate: true,
      // && this.stageStudentNum > 0
      handler(val) {
        if (this.deviceType === "camera") {
          this.activeDeviceId = val;
        }
      }
    }
  },
  methods: {
    async getDeviceList() {
      switch (this.deviceType) {
        case "camera":
          this.deviceList =
            // this.stageStudentNum === 0
            //   ? JSON.parse(localStorage.getItem("live_cameraList"))
            //   :
            await TRTC.getCameraList();
          if (
            this.deviceList.find(
              (item) => item.deviceId === localStorage.getItem("live_camera")
            )
          ) {
            this.activeDeviceId = localStorage.getItem("live_camera");
          } else {
            this.activeDeviceId = this.deviceList[0].deviceId;
          }
          // &&
          // this.stageStudentNum > 0
          if (!localStorage.getItem("live_camera")) {
            this.$eventBus.$emit("startLocalVideo");
          }
          break;
        case "microphone":
          this.deviceList =
            // this.stageStudentNum === 0
            //   ? JSON.parse(localStorage.getItem("live_audioList"))
            //   :
            await TRTC.getMicrophoneList();
          if (
            this.deviceList.find(
              (item) =>
                item.deviceId === localStorage.getItem("live_microphone")
            )
          ) {
            this.activeDeviceId = localStorage.getItem("live_microphone");
          } else {
            this.activeDeviceId = this.deviceList[0].deviceId;
          }
          console.log("this.deviceList", this.deviceList);
          console.log("this.activeDeviceId", this.activeDeviceId);
          // &&
          // this.stageStudentNum > 0
          if (!localStorage.getItem("live_microphone")) {
            this.$eventBus.$emit("startLocalAudio");
          }
          break;
        case "speaker":
          console.log("speakerthis.1111", this.stageStudentNum);
          // if (this.stageStudentNum > 0) {
          this.deviceList = await TRTC.getSpeakerList();
          console.log("speakerthis.deviceList", this.deviceList);
          if (
            this.deviceList.find(
              (item) => item.deviceId === localStorage.getItem("live_spaker")
            )
          ) {
            this.activeDeviceId = localStorage.getItem("live_spaker");
          } else {
            this.activeDeviceId = this.deviceList[0].deviceId;
          }
          // }

          break;
        default:
          break;
      }
    },
    handleChange(deviceId) {
      const device = this.deviceList.find(
        (device) => device.deviceId === deviceId
      );
      console.log("this.deviceType", this.deviceType);
      switch (this.deviceType) {
        case "camera":
          this.$store.dispatch("UPDATE_ACTIVE_CAMERA", device);
          localStorage.setItem("live_camera", device.deviceId);

          break;
        case "microphone":
          this.$store.dispatch("UPDATE_ACTIVE_MICROPHONE", device);
          localStorage.setItem("live_microphone", device.deviceId);

          break;
        case "speaker":
          this.$store.dispatch("UPDATE_ACTIVE_SPEAKER", device);
          localStorage.setItem("live_spaker", device.deviceId);
          break;
        default:
          break;
      }
    },
    async initDeviceList() {
      this.deviceList = await this.getDeviceList(this.deviceType);
      console.log("deviceList", this.deviceList);
      // if (this.deviceType === "microphone") {
      //   this.activeDeviceId = this.deviceList[0].activeDeviceId;
      // }
    }
  },
  mounted() {
    this.stageStudentNum = parseInt(localStorage.getItem("live_studentTotal"));

    this.$eventBus.$on("changeTXLivePushDone", () => {
      this.getDeviceList();
    });
    this.$eventBus.$on("device_change", () => {
      // if (this.stageStudentNum > 0) {
      this.getDeviceList();
      // }
    });
    // if (this.stageStudentNum > 0) {
    navigator.mediaDevices.addEventListener(
      "devicechange",
      this.initDeviceList
    );
    // }
  },
  beforeDestroy() {
    // if (this.stageStudentNum > 0) {
    navigator.mediaDevices.removeEventListener(
      "devicechange",
      this.initDeviceList
    );
    // }
  }
};
</script>

<style lang="less" scoped>
.select {
  width: 300px;
  margin-left: 20px;
  margin-bottom: 10px;
}
.el-select {
  width: 100%;
  border-radius: 6px;
  ::v-deep .el-input {
    width: 100%;
  }
  ::v-deep .el-select__caret {
    color: #fff;
    font-weight: 700;
  }

  ::v-deep .el-input__inner {
    background: #438eff;
    color: #fff;
    font-weight: 500;
  }
  ::v-deep .el-input__inner:focus {
    border-color: #438eff;
  }
  ::v-deep .el-select-dropdown__wrap {
    margin-bottom: 0px !important;
    margin-right: 0px !important;
  }
  ::v-deep .el-select-dropdown__item.selected {
    color: #438eff;
    font-weight: bold;
  }
}
</style>
