<template>
  <!--时间线-->
  <div class="d-f w100" style="height: 100%">
    <!-- 当日志记录不超过设置数量个，全排列 -->
    <div class="timelineVessel-box w100">
      <div :style="{ width: widthx }" class="d-f d-c">
        <template v-for="(item, index) in timeLineList">
          <div :key="index" class="f-1 f-d-c d-f timelineVessel">
            <div class="f-1 d-f">
              <div :class="index === 0 ? 'f-1' : 'item'"></div>
              <div
                class="dot"
                @click="changeActive(index, item.cycle_date)"
                :class="{ active: index === lineIndex }"
              ></div>
              <div
                :class="index === timeLineList.length - 1 ? 'f-1' : 'item'"
              ></div>
            </div>
            <div class="item_bottom">
              <section
                class="textBox"
                :class="{ activeText: index === lineIndex }"
                @click="changeActive(index, item.cycle_date)"
              >
                <div>{{ timeLineList[index].cycle_date | getDate }}</div>
                <div>{{ timeLineList[index].operator_type_chn }}</div>
              </section>
            </div>
          </div>
        </template>
      </div>
    </div>
    <!-- 当日志记录超过设置数量个，可以左右滑动 -->
  </div>
</template>

<script>
export default {
  name: "timeLine",
  data() {
    return {
      point: 0, // 初始位置
      point_end: 11 // 初始展示几个节点
    };
  },
  props: ["isAll", "timeLineList", "chooseIndex"], // 后续获得查询当前学员所需要的信息

  computed: {
    lineIndex() {
      return this.chooseIndex ?? 0;
    },
    widthx() {
      return this.timeLineList.length
        ? this.timeLineList.length * 110 + "px"
        : "100%";
    }
  },
  methods: {
    changeActive(index, time) {
      // this.chooseIndex = index;
      this.$emit("update:chooseIndex", index);
      this.$emit("changeTableIndex", index, time);
    },
    moveLeft() {
      if (this.point > 0) {
        this.point -= 1;
        this.point_end -= 1;
      }
    },
    moveRight() {
      if (this.point_end < this.timeLineList.length - 1) {
        this.point += 1;
        this.point_end += 1;
      }
    },
    // 重置当前状态
    reset() {
      // this.chooseIndex = 0;
      this.$emit("update:chooseIndex", 0);
      this.$emit("changeTableIndex", 0, "");
    },
    // 反转当前时间
    flashBackTime() {
      this.reset();

      this.timeLineList.reverse();
    }
  }
};
</script>

<style scoped lang="less">
.timelineVessel-box {
  overflow-x: auto;
}
.w100 {
  width: 100%;
}
.d-f {
  display: flex;
  width: 100%;
}
.d-c {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 100%;
  overflow-x: auto;
  position: relative;
  box-sizing: border-box;
  padding: 0 10px;
}
.f-d-c {
  flex-direction: column;
}
.d-f-l {
  position: sticky;
  left: 0;
}
.d-f-r {
  position: sticky;
  right: 0;
}
.f-1 {
  flex: 1;
}
.j-c-sa {
  justify-content: space-around;
}
.container {
  height: 100%;
  margin-left: 0px;
  padding-left: 0px;
  margin-bottom: 5px;
}
.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: white;
  margin: 5px 0px;
  box-sizing: border-box;
  border: 1px solid #2d80ed;
}
.item {
  flex: 1;
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 9px;
  box-sizing: border-box;
  width: 35px;
}
.item_bottom {
  /* flex: 1; */
  text-align: center;
  height: 15px;
  margin-top: 7px;
  font-size: 14px;
  position: relative;
}
.timelineVessel {
  // width: 100px;
}
.timelineVessel:nth-child(even) section {
  position: absolute;
  top: -110px;
  width: 100px;
  text-align: center;
  left: 50%;
  transform: translateX(-50%);
  &::before {
    position: absolute;
    top: 70px;
    left: 50%;
    transform: translateX(-50%);
    content: "";
    border-bottom: 8px solid transparent;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid #f2f3f5;
  }
}
.timelineVessel:nth-child(odd) section {
  position: absolute;
  top: 8px;
  width: 100px;
  text-align: center;
  left: 50%;
  transform: translateX(-50%);
  &::before {
    position: absolute;
    top: -16px;
    left: 50%;
    transform: translateX(-50%);
    content: "";
    border-bottom: 8px solid #f2f3f5;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid transparent;
  }
}
.move-button {
  width: 40px;
  height: 18px;
  background: white;
  text-align: center;
  box-sizing: border-box;
  color: #2d80ed;
  font-weight: bold;
  font-size: 20px;
  cursor: pointer;
}
.active {
  background-color: #2d80ed !important;
}
.activeText {
  color: #2d80ed !important;
  background-color: #ebf4ff !important;
}
.timelineVessel:nth-child(odd) .activeText {
  &::after {
    position: absolute;
    top: -16px;
    left: 50%;
    transform: translateX(-50%);
    content: "";
    border-bottom: 8px solid #ebf4ff !important;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid transparent;
  }
}
.timelineVessel:nth-child(even) .activeText {
  &::after {
    position: absolute;
    top: 98px;
    left: 50%;
    transform: translateX(-50%);
    content: "";
    border-top: 8px solid #ebf4ff !important;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid transparent;
  }
}

.textBox {
  width: 78px;
  height: 60px;
  background: #f2f3f5;
  border-radius: 4px;
  font-size: 14px;
  color: #8492a6;
  line-height: 2;
  padding: 5px;
  cursor: pointer;
}
</style>
