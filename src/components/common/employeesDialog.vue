<!--选择员工-->
<template>
  <div class="employee-staff">
    <el-dialog
      :visible="true"
      title="选择员工"
      width="1000px"
      class="employee-dialog"
      :modal="true"
      :append-to-body="true"
      :before-close="handleClose"
    >
      <div v-loading="loading" class="tg-dialog__content">
        <div class="organization-tree-check">
          <div class="search">
            <div class="search-input">
              <!-- <img src="@/assets/图片/icon_search_grey.png" alt /> -->
              <el-input
                v-model="filter_text"
                placeholder="请输入员工或部门名称"
                clearable
                style="width: 100%"
                prefix-icon="el-icon-search"
              />
            </div>

            <el-checkbox v-model="is_leave" @change="leave_change">
              显示离职员工
            </el-checkbox>
          </div>
          <el-tree
            show-checkbox
            ref="mytree"
            highlight-current
            node-key="id"
            :data="tree_list"
            :props="defaultProps"
            :accordion="true"
            :filter-node-method="filterNode"
            :default-expanded-keys="defaultExpandedKeys"
            @check="checkBoxChange"
            @check-change="checkChange"
          >
            <span class="custom-tree-node" slot-scope="{ node, data }">
              <!--  :class="{ 'is-disabled': node.disabled }" -->
              <span
                @click="spanRowClick(node)"
                v-if="data.data_type === 'employee'"
                >{{ `${data.name}【${data?.post_office_name?.join()}】` }}
                <el-tag size="small" v-if="data.is_leave" type="danger"
                  >离职</el-tag
                >
              </span>
              <span :class="{ 'is-disabled': node.disabled }" v-else>{{
                `${data.name}`
              }}</span>
            </span>
          </el-tree>
        </div>
        <div class="staff-list--right">
          <div class="organization__title">
            <span>
              已选人员
              <em>{{ right_staff_list.length }}</em>
            </span>
            <span class="all-clear" @click="clear">
              <img src="@/assets/图片/icon_clear.png" alt />
              清空
            </span>
          </div>
          <div
            class="organization__info"
            v-for="(item, index) in right_staff_list"
            :key="index"
          >
            <span>{{ item.name }} </span>
            <el-tag
              v-if="item.is_leave"
              style="margin-right: 10px"
              size="small"
              type="danger"
              >离职</el-tag
            >
            <img
              src="@/assets/图片/icon_close_green.png"
              alt
              @click="delOne(index, item.id)"
            />
          </div>
          <span v-if="right_staff_list.length === 0" class="is-empty"
            >暂无数据</span
          >
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="tg-button--plain" type="plain" @click="back"
          >取消</el-button
        >
        <el-button class="tg-button--primary" type="primary" @click="really"
          >确定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import staffApi from "@/api/staff";
export default {
  name: "EmployeesDialog",
  data() {
    return {
      right_staff_list: [],
      check_staff: "",
      staff_list: [],
      filter_text: "",
      defaultProps: {
        children: "child",
        label: "name"
      },
      tree_list: [],
      loading: false,
      open_level: 1, // 设置默认展开的级别
      defaultExpandedKeys: [],
      is_leave: false
    };
  },
  props: {
    // contain_leave: {
    //   // 查询是否包含离职员工数据
    //   type: Boolean,
    //   default: false
    // },
    allow_submit_leave: {
      // 是否允许提交离职员工数据
      type: Boolean,
      default: false
    },
    check_type: {
      // 选择类型  single 单选 ; multiple 多选
      type: String,
      default: "multiple"
    },
    department_ids: Array, // 是否通过校区筛选
    checked_ids: {
      // 选中的员工ids
      type: Array,
      default: () => []
    }
  },
  computed: {
    // school_id() {
    //   return this.$store.getters.doneGetSchoolId;
    // }
  },
  watch: {
    filter_text(val) {
      this.$refs.mytree.filter(val);
    }
  },
  created() {
    // this.is_leave = this.contain_leave;
    this.getTreeData();
  },
  methods: {
    init_page() {
      this.right_staff_list = this.get_right_employee();
      this.setDefaultExpandedKeys();
      this.init_checked();
    },
    leave_change() {
      this.getTreeData();
    },
    get_right_employee() {
      const { checked_ids } = this;
      const arr = [];
      function getChildItem(item) {
        if (item.data_type === "employee" && checked_ids.includes(item.id)) {
          arr.push(item);
        }
        if (item.child) {
          item.child.forEach((item) => {
            getChildItem(item);
          });
        }
      }
      getChildItem(this.tree_list[0]);
      return arr;
    },
    setDefaultExpandedKeys() {
      const level = this.open_level;
      this.defaultExpandedKeys = this.getKeysByLevel(this.tree_list, level);
    },
    getKeysByLevel(data, level, currentLevel = 1) {
      let ids = [];
      // 默认展开到二级菜单
      if (currentLevel <= level) {
        for (const node of data) {
          if (node.data_type === "department") {
            ids.push(node.id);
            if (node.child) {
              ids = ids.concat(
                this.getKeysByLevel(node.child, level, currentLevel + 1)
              );
            }
          }
        }
      }
      // 左侧选中的员工在树中默认展开
      const employee_ids = this.checked_ids;
      ids = ids.concat(employee_ids);
      return ids;
    },
    handleClose() {
      this.$emit("close");
    },

    filterNode(value, data, node) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
      // if (!value) return true;
      // let parentNode = node.parent;
      // let labels = [node.label];
      // let level = 1;
      // while (level < node.level) {
      //   labels = [...labels, parentNode.label];
      //   parentNode = parentNode.parent;
      //   level++;
      // }
      // return labels.some((label) => label.indexOf(value) !== -1);
    },
    init_checked() {
      const ids = this.right_staff_list.map((item) => item.id);
      this.$refs.mytree.setCheckedKeys(ids);
    },
    delOne(index) {
      this.right_staff_list.splice(index, 1);
      this.init_checked();
    },
    clear() {
      this.right_staff_list = [];
      this.init_checked();
    },
    back() {
      this.$emit("close");
    },
    really() {
      if (!this.allow_submit_leave) {
        const bool = this.right_staff_list.some((item) => item.is_leave);
        if (bool) {
          this.$message.info("不能选择离职员工，请重新选择！");
          return;
        }
      }
      this.$emit("change", this.right_staff_list);
      this.$emit("close");
    },

    // 获取组织架构数据
    getTreeData() {
      const { department_ids, is_leave } = this;
      const params = {
        is_contain_leave: is_leave
      };
      if (department_ids) {
        params.department_id = department_ids;
      }
      this.loading = true;
      staffApi
        .getStaffFullTreeList(params)
        .then((res) => {
          this.loading = false;
          const { data } = res;
          if (data) {
            this.tree_list = [data];
            this.formatTreeData(this.tree_list);
            this.init_page();
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 递归处理特殊需求
    formatTreeData(d) {
      const { check_type } = this;
      function processItem(item) {
        // 部门下没有员工的节点不能选择
        if (item.data_type === "department" && !item.child?.length) {
          item.disabled = true;
        }
        // 如果是单选
        if (check_type === "single") {
          if (item.data_type === "department") {
            item.disabled = true;
          }
        }
        // 是员工，但是已经离职不可选
        // if (item.data_type === "employee" && item.is_leave) {
        //   item.disabled = true;
        // }
        if (item.child) {
          item.child.forEach(processItem);
        }
      }
      d.forEach(processItem);
    },
    singleClick(nodeKey) {
      if (this.check_type === "single") {
        this.$refs.mytree.setCheckedKeys([]);
        this.$refs.mytree.setChecked(nodeKey, true);
      }
    },
    spanRowClick(node) {
      // console.log(node);
      // 离职员工不可选
      // if (val.data.is_leave) {
      //   return;
      // }

      node.checked = !node.checked;
      this.singleClick(node.data.id);
    },
    checkBoxChange(data) {
      this.singleClick(data.id);
    },
    checkChange() {
      const arr = this.$refs.mytree.getCheckedNodes();
      // 递归找出员工
      const fn = (item) => {
        if (item.data_type === "employee") {
          return item;
        }
        if (item.has_child) {
          item.child.forEach((child) => {
            fn(child);
          });
        }
      };
      const employee_arr = [];
      arr.forEach((child) => {
        if (fn(child)) {
          employee_arr.push(fn(child));
        }
      });
      this.right_staff_list = employee_arr;
    }
  }
};
</script>
<style lang="less" scoped>
.employee-staff {
  .btn__img--dotted {
    margin-right: 10px;
  }
}
.custom-tree-node {
  .is-disabled {
    cursor: not-allowed;
    color: #8492a6;
  }
}
.employee-dialog {
  .search {
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
    .search-input {
      flex: 1;
      margin-right: 20px;
    }
    img {
      width: 14px;
      height: 14px;
      margin-right: 12px;
      margin-left: 11px;
      position: absolute;
      top: 9px;
      left: 6px;
    }
  }

  /deep/ .el-dialog__body {
    padding: 0;
  }

  .tg-dialog__content {
    display: flex;
    flex-direction: row;
    height: 430px;
  }

  .organization-tree-check {
    width: 60%;
    border-right: 1px solid #e0e6ed;
    padding: 16px;
    height: auto;
    overflow: auto;
    box-sizing: border-box;
  }

  .staff-list--right {
    flex: 1;
    width: auto;
    height: auto;
    overflow: auto;
    padding: 16px;
    box-sizing: border-box;
    .organization__title,
    .organization__info {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }

    .all-clear {
      color: #157df0;
      font-size: @text-size_small;
      font-family: @text-famliy_medium;
      cursor: pointer;

      img {
        width: 14px;
        height: 14px;
        margin-right: 8px;
        vertical-align: middle;
        margin-top: -3px;
      }
    }

    .organization__title {
      margin-bottom: 16px;
      em {
        font-style: normal;
        color: @base-color;
      }
    }

    .organization__info {
      border: 1px solid @base-color;
      border-radius: 4px;
      height: 40px;
      align-items: center;
      padding: 0 16px;
      margin-bottom: 16px;

      .el-input,
      .el-input__inner {
        height: 40px;
        line-height: 40px;
      }

      img {
        width: 16px;
        height: 16px;
        cursor: pointer;
      }

      span:nth-child(1) {
        overflow-x: auto;
        width: calc(100% - 36px);
        white-space: nowrap;
      }
    }

    .required {
      &::before {
        content: "*";
        margin-right: 5px;
        color: #ff0317;
      }
    }
  }

  .tree__checkbox.el-checkbox {
    margin-right: 16px;
    margin-top: -1px;
  }

  .is-empty {
    color: @text-color_third;
    width: 100%;
    display: inline-block;
    text-align: center;
    line-height: 350px;
  }
  .custom-tree-node_courseStall {
    display: flex;
    .title_text {
      width: 150px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .el-tree-node__content {
    height: 30px;
  }

  .el-tree {
    padding-top: 11px;
  }

  .is-readonly {
    pointer-events: none;
  }

  .border--active {
    position: relative;

    &::after {
      content: "";
      position: absolute;
      width: 100%;
      height: 32px;
      left: -2px;
      top: -2px;
      border: 2px solid #ebf4ff;
      border-radius: 6px;
      z-index: 10;
    }

    .el-input__inner {
      border-color: @base-color;
    }
  }
}
</style>
