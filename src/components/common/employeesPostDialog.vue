<!--选择员工岗位-->
<template>
  <div class="employee-staff">
    <el-dialog
      :visible="true"
      title="选择岗位"
      width="800px"
      class="employee-dialog"
      :modal="true"
      :append-to-body="true"
      :before-close="handleClose"
    >
      <div v-loading="loading" class="tg-dialog__content">
        <div class="organization-tree-check">
          <div class="search">
            <div class="search-input">
              <!-- <img src="@/assets/图片/icon_search_grey.png" alt /> -->
              <el-input
                v-model="filter_text"
                placeholder="请输入关键字"
                clearable
                style="width: 100%"
                prefix-icon="el-icon-search"
              />
            </div>
          </div>
          <el-tree
            show-checkbox
            ref="mytree"
            highlight-current
            node-key="id"
            :data="tree_list"
            :props="defaultProps"
            :check-on-click-node="true"
            :filter-node-method="filterNode"
            @check-change="spanRowClick"
          >
            <!-- <span class="custom-tree-node" slot-scope="{ node, data }">
              <span
                :class="{ 'is-disabled': node.disabled }"
                @click="spanRowClick(node)"
                v-if="data.data_type === 'employee'"
                >{{ data.name }}
                <el-tag size="small" v-if="data.is_leave" type="danger"
                  >离职</el-tag
                >
              </span>
              <span :class="{ 'is-disabled': node.disabled }" v-else>{{
                `${data.name}`
              }}</span>
            </span> -->
          </el-tree>
        </div>
        <div class="staff-list--right">
          <div class="organization__title">
            <span>
              已选岗位
              <em>{{ right_staff_list.length }}</em>
            </span>
            <span class="all-clear" @click="clear">
              <img src="@/assets/图片/icon_clear.png" alt />
              清空
            </span>
          </div>
          <div
            class="organization__info"
            v-for="(item, index) in right_staff_list"
            :key="index"
          >
            <span>{{ item.name }} </span>
            <el-tag
              v-if="item.is_leave"
              style="margin-right: 10px"
              size="small"
              type="danger"
              >离职</el-tag
            >
            <img
              src="@/assets/图片/icon_close_green.png"
              alt
              @click="delOne(index, item.id)"
            />
          </div>
          <span v-if="right_staff_list.length === 0" class="is-empty"
            >暂无数据</span
          >
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="tg-button--plain" type="plain" @click="back"
          >取消</el-button
        >
        <el-button class="tg-button--primary" type="primary" @click="really"
          >确定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import staffApi from "@/api/staff";
export default {
  name: "EmployeesPostDialog",
  data() {
    return {
      right_staff_list: [],
      filter_text: "",
      defaultProps: {
        children: "child",
        label: "name"
      },
      tree_list: [],
      loading: false,
      ids: []
    };
  },
  props: {
    check_type: {
      // 选择类型  single 单选 ; multiple 多选
      type: String,
      default: "multiple"
    },

    checked_ids: {
      // 选中的岗位ids
      type: Array,
      default: () => []
    }
  },
  computed: {
    // school_id() {
    //   return this.$store.getters.doneGetSchoolId;
    // }
    // nodeKey() {
    //   return this.choose_type === "employee" ? "id" : "uid";
    // }
  },
  watch: {
    filter_text(val) {
      this.$refs.mytree.filter(val);
    }
  },
  created() {
    this.ids = this.checked_ids;
    this.getTreeData();
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },

    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    init_checked() {
      const arr = [];
      this.ids.map((id) => {
        this.tree_list.map((item) => {
          if (item.id === id) {
            arr.push(item);
          }
        });
      });
      this.right_staff_list = arr;
      // const ids = arr.map((item) => {
      //   return item.id;
      // });
      this.$refs.mytree.setCheckedKeys(this.ids);
    },
    delOne(index) {
      console.log(index);
      this.ids.splice(index, 1);
      this.init_checked();
    },
    clear() {
      this.ids = [];
      this.init_checked();
    },
    back() {
      this.$emit("close");
    },
    really() {
      this.$emit("change", this.right_staff_list);
      this.$emit("close");
    },

    // 获取组织架构数据
    getTreeData() {
      const { department_ids, is_leave } = this;
      const params = {
        is_contain_leave: is_leave
      };
      if (department_ids) {
        params.department_id = department_ids;
      }
      this.loading = true;

      staffApi
        .getEmployeePostTree(params)
        .then((res) => {
          this.loading = false;
          const { data } = res.data;
          if (data) {
            this.tree_list = data.map((item) => {
              const { id, name } = item;
              return {
                id,
                name,
                child: []
              };
            });
            this.init_checked();
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },

    spanRowClick(data, checked) {
      console.log(data, checked);

      if (data.disabled) {
        return;
      }

      if (this.check_type === "single") {
        // this.$refs.mytree.setCheckedKeys([]);
        if (checked) {
          this.ids = [data.id];
        } else {
          this.ids = [];
        }
        this.init_checked();
      } else {
        if (checked) {
          this.right_staff_list.push(data);
        } else {
          this.right_staff_list = this.right_staff_list.filter(
            (item) => item.id !== data.id
          );
        }
      }
      this.ids = this.right_staff_list.map((item) => item.id);
    }
  }
};
</script>
<style lang="less" scoped>
.employee-staff {
  .btn__img--dotted {
    margin-right: 10px;
  }
}
.custom-tree-node {
  .is-disabled {
    cursor: not-allowed;
    color: #8492a6;
  }
}
.employee-dialog {
  .search {
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
    .search-input {
      flex: 1;
      margin-right: 20px;
    }
    img {
      width: 14px;
      height: 14px;
      margin-right: 12px;
      margin-left: 11px;
      position: absolute;
      top: 9px;
      left: 6px;
    }
  }

  /deep/ .el-dialog__body {
    padding: 0;
  }

  .tg-dialog__content {
    display: flex;
    flex-direction: row;
    height: 430px;
  }

  .organization-tree-check {
    width: 60%;
    border-right: 1px solid #e0e6ed;
    padding: 16px;
    height: auto;
    overflow: auto;
    box-sizing: border-box;
  }

  .staff-list--right {
    flex: 1;
    width: auto;
    height: auto;
    overflow: auto;
    padding: 16px;
    box-sizing: border-box;
    .organization__title,
    .organization__info {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }

    .all-clear {
      color: #157df0;
      font-size: @text-size_small;
      font-family: @text-famliy_medium;
      cursor: pointer;

      img {
        width: 14px;
        height: 14px;
        margin-right: 8px;
        vertical-align: middle;
        margin-top: -3px;
      }
    }

    .organization__title {
      margin-bottom: 16px;
      em {
        font-style: normal;
        color: @base-color;
      }
    }

    .organization__info {
      border: 1px solid @base-color;
      border-radius: 4px;
      height: 40px;
      align-items: center;
      padding: 0 16px;
      margin-bottom: 16px;

      .el-input,
      .el-input__inner {
        height: 40px;
        line-height: 40px;
      }

      img {
        width: 16px;
        height: 16px;
        cursor: pointer;
      }

      span:nth-child(1) {
        overflow-x: auto;
        width: calc(100% - 36px);
        white-space: nowrap;
      }
    }

    .required {
      &::before {
        content: "*";
        margin-right: 5px;
        color: #ff0317;
      }
    }
  }

  .tree__checkbox.el-checkbox {
    margin-right: 16px;
    margin-top: -1px;
  }

  .is-empty {
    color: @text-color_third;
    width: 100%;
    display: inline-block;
    text-align: center;
    line-height: 350px;
  }
  .custom-tree-node_courseStall {
    display: flex;
    .title_text {
      width: 150px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .el-tree-node__content {
    height: 30px;
  }

  .el-tree {
    padding-top: 11px;
  }

  .is-readonly {
    pointer-events: none;
  }

  .border--active {
    position: relative;

    &::after {
      content: "";
      position: absolute;
      width: 100%;
      height: 32px;
      left: -2px;
      top: -2px;
      border: 2px solid #ebf4ff;
      border-radius: 6px;
      z-index: 10;
    }

    .el-input__inner {
      border-color: @base-color;
    }
  }
}
</style>
