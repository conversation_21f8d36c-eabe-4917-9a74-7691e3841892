<template>
  <div class="chooseBox">
    <div
      v-for="(item, index) in chooseList"
      :key="index"
      class="d-i box"
      :class="[
        list.indexOf(item.config_value) != -1 ? 'action' : '',
        item.config_name.length >= 5 ? 'box-large' : ''
      ]"
      @click="choose(item.config_value)"
    >
      {{ item.config_name }}
      <span
        v-if="
          list.indexOf(item.config_value) != -1 &&
          item.config_value != 'infinite'
        "
        class="m-l-15"
      >
        |<i class="el-icon-close f-w m-l-9"></i>
      </span>
    </div>
  </div>
</template>

<script>
export default {
  props: ["chooseList", "list"],
  components: {},
  data() {
    return {};
  },
  mounted() {
    this.choose();
  },
  methods: {
    choose(item) {
      if (item) {
        if (this.list.indexOf(item) === -1) {
          const newVal = item ? [...this.list, item] : [...this.list];
          if (newVal[newVal.length - 1] !== "infinite" && newVal.length >= 1) {
            if (newVal.indexOf("infinite") !== -1) {
              const i = newVal.findIndex((item) => item === "infinite");
              newVal.splice(i, 1);
            }
            this.$emit("update:list", newVal);
          } else if (
            newVal[newVal.length - 1] === "infinite" &&
            newVal.length > 1
          ) {
            this.$emit("update:list", ["infinite"]);
          } else if (newVal.length === 0) {
            this.$emit("update:list", ["infinite"]);
          }
        } else {
          const i = this.list.findIndex((val) => val === item);
          this.list.splice(i, 1);
          if (this.list.length === 0) {
            this.$emit("update:list", ["infinite"]);
          } else {
            this.$emit("update:list", this.list);
          }
        }
        this.$emit("change");
      }
    }
  }
};
</script>
<style scoped>
.chooseBox {
  max-width: 1100px;
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}
.d-i {
  display: inline-block;
}
.box {
  width: 96px;
  max-width: 126px;
  height: 32px;
  text-align: center;
  font-size: 14px;
  line-height: 32px;
  border: 1px solid #2d80ed;
  color: #2d80ed;
  border-radius: 4px;
  cursor: pointer;
  margin: 0 10px;
  user-select: none;
}
.box:first-child {
  width: 72px !important;
  height: 32px;
}
.box:hover {
  background: #ebf4ff;
  color: #2d80ed;
}
.action {
  background: #2d80ed;
  color: #fff;
}
.f-w {
  font-weight: bold;
}
.m-l-15 {
  margin-left: 15px;
}
.m-l-9 {
  margin-left: 9px;
}
.box-large {
  width: 136px;
}
</style>
