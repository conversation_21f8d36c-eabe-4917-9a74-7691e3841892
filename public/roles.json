{"code": 200, "msg": "操作成功", "data": {"navigatepageNums": [], "startRow": "1", "hasNextPage": "false", "prePage": "0", "nextPage": "0", "endRow": "8", "pageSize": "0", "list": [{"code": "SHGLY", "roleId": "1", "scope": "1", "roleName": "审核管理员", "description": "初始化内置审批角色", "status": "1"}, {"code": "", "roleId": "2", "scope": "1", "roleName": "招商事业部", "description": "", "status": "1"}, {"code": "", "roleId": "3", "scope": "1", "roleName": "互联网部门", "description": "", "status": "1"}, {"code": "", "roleId": "4", "scope": "1", "roleName": "销售部", "description": "", "status": "1"}, {"code": "", "roleId": "5", "scope": "1", "roleName": "战区一", "description": "", "status": "1"}, {"code": "", "roleId": "6", "scope": "1", "roleName": "战区二", "description": "", "status": "1"}, {"code": "", "roleId": "7", "scope": "1", "roleName": "JAVA开发", "description": "", "status": "1"}, {"code": "", "roleId": "8", "scope": "1", "roleName": "测试审批角色", "description": "", "status": "1"}], "pageNum": "1", "navigatePages": "8", "navigateFirstPage": "0", "total": "8", "pages": "0", "size": "8", "isLastPage": "true", "hasPreviousPage": "false", "navigateLastPage": "0", "isFirstPage": "true"}}