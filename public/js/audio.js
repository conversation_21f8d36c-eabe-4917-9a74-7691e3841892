// 需要使用到的元素对象
let stateSet = null;
let musicHead = null;
let musicOBJ = null;
let proLine = null;
let timeshow = null;
let musicLineMoveFa = null;
let musicLineMove = null
let volumeBtn = null;
let volumeBar = null;
let volumePro = null;
let musicLast = null;
let musicNext = null;
let musicBOXbc = null;
let musicInfoShow = null;

// 当前的网页加载完毕的时候触发的方法
// window.onload = function () {
//   musicPlaySet();
//   musicMove();
//   musicListen();
//   volumeSet();
//   musicJump();
// };

// 初始化变量
let a_time = null;
let a_time2 = null;
let a_time3 = null;
let a_time4 = null;
let a_f1 = false;
let a_f2 = true;
let a_i1 = 0;
let a_nowtime = 0;
let a_currtime = 0;

function getDom() {
  stateSet = document.querySelector(".my-audio-player .state_set");
  musicHead = document.querySelector(".my-audio-player .head");
  musicOBJ = document.querySelector(".my-audio-player audio");
  proLine = document.querySelector(".my-audio-player .pros");
  timeshow = document.querySelector(".my-audio-player .time_show");
  musicLineMoveFa = document.querySelector(".my-audio-player .progress");
  musicLineMove = document.querySelector(".my-audio-player .pros");
  volumeBtn = document.querySelector(".my-audio-player .volume");
  volumeBar = document.querySelector(".my-audio-player .volume_range");
  volumePro = document.querySelector(".my-audio-player .range");
  musicLast = document.querySelector(".my-audio-player .last");
  musicNext = document.querySelector(".my-audio-player .next");
  musicBOXbc = document.querySelector(".my-audio-player .play_bc > img");
  musicInfoShow = document.querySelector(".my-audio-player .music_info");
}

// 音乐的开始和暂停方法
function musicPlaySet() {
  stateSet.onclick = function () {
    // 点击暂停的时候
    if (a_f1) {
      musicStop();
      // 点击开始的时候
    } else {
      stateSet.className = "state_set iconfont icon-24gf-pause2";
      musicOBJ.play();
      musicTime();
      a_time = setInterval(() => {
        a_i1++;
        musicHead.style.transform = `rotate(${a_i1}deg)`;
      }, 20);
      a_f1 = true;
    }
  };
}

// 音乐对象的时间的显示方法
function musicTime() {
  // musicOBJ.addEventListener('loadedmetadata',  ()=> {
  // const duration = musicOBJ.duration;
  const time = Math.floor(musicOBJ.duration);
  let minute = Math.floor(time / 60) + "";
  minute = minute < 10 ? "0" + minute : minute;
  let second = (time % 60) + "";
  second = second < 10 ? "0" + second : second;
  a_time4 = setInterval(() => {
    const muscurrtime = musicOBJ.currentTime;
    let currminute = Math.floor(muscurrtime / 60) + "";
    currminute = currminute < 10 ? "0" + currminute : currminute;
    let currsecond = Math.floor(muscurrtime % 60) + "";
    currsecond = currsecond < 10 ? "0" + currsecond : currsecond;
    timeshow.innerText = `${currminute}:${currsecond} / ${minute}:${second}`;
  }, 1);
  a_time2 = setInterval(() => {
    const movetime = 310 / time;
    a_nowtime += movetime;
    proLine.style.width = a_nowtime + "px";
  }, 1000);
  // });
}

// 音乐进度拖动方法
function musicMove() {
  musicLineMove.onmousedown = function () {
    clearInterval(a_time);
    clearInterval(a_time2);
    clearInterval(a_time4);
    musicOBJ.pause();
    stateSet.className = "state_set iconfont icon-bofang";
    musicLineMoveFa.onmousemove = function () {
      clearInterval(a_time);
      clearInterval(a_time2);
      clearInterval(a_time4);
      a_x = event.clientX - (window.innerWidth - 310) / 2 - 30;
      proLine.style.width = a_x + "px";
      a_currtime = a_x / (310 / musicOBJ.duration);
    };
  };
  musicLineMove.onmouseup = function () {
    musicLineMoveFa.onmousemove = null;
    musicOBJ.currentTime = a_currtime;
    proLine.style.width = a_currtime * (310 / musicOBJ.duration) + "px";
    a_nowtime = a_currtime * (310 / musicOBJ.duration);
    musicTime();
    musicOBJ.play();
    stateSet.className = "state_set iconfont icon-24gf-pause2";
    a_time = setInterval(() => {
      a_i1++;
      musicHead.style.transform = `rotate(${a_i1}deg)`;
    }, 20);
    a_f1 = true;
  };
}

// 监听音乐的状态
function musicListen() {
  a_time3 = setInterval(() => {
    if (musicOBJ.ended) {
      proLine.style.width = "0px";
      stateSet.className = "state_set iconfont icon-bofang";
      a_nowtime = 0;
      clearInterval(a_time);
      clearInterval(a_time2);
      clearInterval(a_time3);
      clearInterval(a_time4);
      a_f1 = false;
    }
  }, 1000);
}

// 音乐的音量调节
function volumeSet() {
  volumePro.autofocus = true;
  volumePro.defaultValue = 30;
  volumePro.step = 1;
  volumePro.max = 100;
  volumePro.min = 0;
  volumeBtn.onmouseenter = function () {
    volumeBar.style.width = "100px";
    volumeBar.style.padding = "3px 6px";
    // volumeBar.style.left = "0px";
    volumeBtn.onclick = function () {
      if (a_f2) {
        volumePro.disabled = true;
        volumeBtn.className = "volume iconfont icon-volumeDisable";
        musicOBJ.muted = true;
        a_f2 = false;
      } else {
        volumePro.disabled = false;
        volumeBtn.className = "volume iconfont icon-volumeMiddle";
        musicOBJ.muted = false;
        a_f2 = true;
      }
    };
  };
  volumeBtn.onmouseleave = function () {
    volumeBar.style.width = "0px";
    volumeBar.style.padding = "0px";
    // volumeBar.style.left = "0px";
  };
  volumePro.onfocus = function () {
    volumeBtn.onclick = null;
    this.onchange = function () {
      musicOBJ.volume = this.value / 100;
      // console.log(this.value);
      if (this.value === 0) {
        volumeBtn.className = "volume iconfont icon-volumeDisable";
        a_f2 = false;
      }
    };
  };
}

// 上一个曲目和下一个曲目方法
const musicList = [
  "LostMemory",
  "You",
  "Keep You Mine",
  "Over Again",
  "Lullaby"
];
//切换音频
function switchAudio() {
  a_nowtime = 0;
  musicStop();
  // musicOBJ.play();
  setTimeout(() => {
    stateSet.click();
  }, 1000);

  // musicTime();
}
function musicJump() {
  for (let i = 0; i < musicList.length; i++) {
    musicLast.onclick = function () {
      musicInfoShow.innerText = musicList[i];
      musicOBJ.src = "music/" + musicList[i] + ".mp3";
      musicBOXbc.src = "head/" + musicList[i] + ".jpg";
      musicHead.src = "head/" + musicList[i] + ".jpg";
      a_nowtime = 0;
      // console.log(i);
      i--;
      if (i < 0 || i > 5) {
        i = musicList.length - 1;
      }
      musicStop();
    };
    musicNext.onclick = function () {
      musicInfoShow.innerText = musicList[i];
      musicOBJ.src = "music/" + musicList[i] + ".mp3";
      musicBOXbc.src = "head/" + musicList[i] + ".jpg";
      musicHead.src = "head/" + musicList[i] + ".jpg";
      a_nowtime = 0;
      // console.log(i);
      i++;
      if (i < 0 || i >= 5) {
        i = 0;
      }
      musicStop();
    };
  }
}

// 音乐停止状态
function musicStop() {
  stateSet.className = "state_set iconfont icon-bofang";
  musicOBJ.pause();
  clearInterval(a_time);
  clearInterval(a_time2);
  clearInterval(a_time4);
  a_f1 = false;
}

function clearAudioPlayer() {

  clearInterval(a_time);
  clearInterval(a_time2);
  clearInterval(a_time3);
  clearInterval(a_time4);

  a_f1 = false;
  a_f2 = true;
  a_i1 = 0;
  a_nowtime = 0;
  a_currtime = 0;
}